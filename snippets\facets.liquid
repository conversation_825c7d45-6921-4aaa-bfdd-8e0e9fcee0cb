{%- comment -%}
  Collection Facets/Filters Snippet
  
  Renders Shopify's native Search & Discovery filtering interface
  for collection pages with proper accessibility and styling.
{%- endcomment -%}

{%- liquid
  assign sort_by = collection.sort_by | default: collection.default_sort_by
-%}

<div class="facets" id="FacetFiltersForm" data-id="{{ section.id }}">
  <form class="facets__form" id="FacetFiltersFormMobile">
    
    {%- if collection.filters != empty -%}
      <details class="facets__disclosure">
        <summary class="facets__summary">
          <span>{{ 'products.facets.filter_button' | t }}</span>
          {%- render 'icon-caret' -%}
        </summary>
        
        <div class="facets__display">
          {%- for filter in collection.filters -%}
            <details class="facets__disclosure-vertical">
              <summary class="facets__summary">
                {{ filter.label | escape }}
                {%- render 'icon-caret' -%}
              </summary>
              
              <div class="facets__list">
                {%- case filter.type -%}
                  {%- when 'list' -%}
                    {%- for value in filter.values -%}
                      <div class="facets__item">
                        <input 
                          type="checkbox" 
                          name="{{ value.param_name }}" 
                          value="{{ value.value }}"
                          id="Filter-{{ filter.param_name }}-{{ forloop.index }}"
                          {% if value.active %}checked{% endif %}
                        >
                        <label for="Filter-{{ filter.param_name }}-{{ forloop.index }}">
                          {{ value.label | escape }}
                          {%- if value.count -%}
                            <span class="facets__count">({{ value.count }})</span>
                          {%- endif -%}
                        </label>
                      </div>
                    {%- endfor -%}
                    
                  {%- when 'price_range' -%}
                    <div class="facets__price">
                      <div class="field">
                        <input 
                          type="number" 
                          name="{{ filter.min_value.param_name }}"
                          id="Filter-{{ filter.label | handle }}-GTE"
                          {%- if filter.min_value.value -%}value="{{ filter.min_value.value | money_without_currency | replace: ',', '' }}"{%- endif -%}
                          placeholder="0"
                          min="0"
                          max="{{ filter.range_max | money_without_currency | replace: ',', '' }}"
                        >
                        <label for="Filter-{{ filter.label | handle }}-GTE">{{ 'products.facets.from' | t }}</label>
                      </div>
                      
                      <div class="field">
                        <input 
                          type="number" 
                          name="{{ filter.max_value.param_name }}"
                          id="Filter-{{ filter.label | handle }}-LTE"
                          {%- if filter.max_value.value -%}value="{{ filter.max_value.value | money_without_currency | replace: ',', '' }}"{%- endif -%}
                          placeholder="{{ filter.range_max | money_without_currency | replace: ',', '' }}"
                          min="0"
                          max="{{ filter.range_max | money_without_currency | replace: ',', '' }}"
                        >
                        <label for="Filter-{{ filter.label | handle }}-LTE">{{ 'products.facets.to' | t }}</label>
                      </div>
                    </div>
                {%- endcase -%}
              </div>
            </details>
          {%- endfor -%}
        </div>
      </details>
    {%- endif -%}

    {%- if collection.products.size > 0 -%}
      <div class="facets__sort">
        <label for="SortBy">{{ 'products.facets.sort_by_label' | t }}</label>
        <select name="sort_by" id="SortBy">
          {%- assign sort_options = 'manual,best-selling,title-ascending,title-descending,price-ascending,price-descending,created-ascending,created-descending' | split: ',' -%}
          {%- for option in sort_options -%}
            <option value="{{ option }}" {% if sort_by == option %}selected="selected"{% endif %}>
              {{ 'products.sorting' | append: '.' | append: option | t }}
            </option>
          {%- endfor -%}
        </select>
      </div>
    {%- endif -%}

    <div class="facets__results-count">
      <span>{{ 'products.facets.product_count_simple' | t: count: collection.products_count }}</span>
    </div>
  </form>
</div>

<script>
  class FacetFilters extends HTMLElement {
    constructor() {
      super();
      this.form = this.querySelector('form');
      this.facetWrapper = this.querySelector('.facets');
      
      this.form.addEventListener('input', this.onFormSubmit.bind(this));
      this.form.addEventListener('change', this.onFormSubmit.bind(this));
    }

    onFormSubmit(event) {
      const formData = new FormData(this.form);
      const searchParams = new URLSearchParams(formData);
      
      // Update URL without page refresh
      const url = `${window.location.pathname}?${searchParams}`;
      window.history.replaceState({}, '', url);
      
      // Fetch and update content
      fetch(url)
        .then(response => response.text())
        .then(responseText => {
          const html = new DOMParser().parseFromString(responseText, 'text/html');
          const newResults = html.querySelector('.collection-grid');
          const currentResults = document.querySelector('.collection-grid');
          
          if (newResults && currentResults) {
            currentResults.innerHTML = newResults.innerHTML;
          }
        });
    }
  }

  customElements.define('facet-filters', FacetFilters);
</script>

<style>
  .facets {
    margin-bottom: 2rem;
  }
  
  .facets__form {
    display: flex;
    gap: 1rem;
    align-items: center;
    flex-wrap: wrap;
  }
  
  .facets__disclosure {
    position: relative;
  }
  
  .facets__summary {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    padding: 0.5rem 1rem;
    border: 1px solid rgba(var(--color-foreground), 0.2);
    border-radius: 4px;
  }
  
  .facets__display {
    position: absolute;
    top: 100%;
    left: 0;
    min-width: 200px;
    background: var(--color-background);
    border: 1px solid rgba(var(--color-foreground), 0.2);
    border-radius: 4px;
    padding: 1rem;
    z-index: 10;
  }
  
  .facets__item {
    margin-bottom: 0.5rem;
  }
  
  .facets__count {
    color: rgba(var(--color-foreground), 0.7);
    font-size: 0.875rem;
  }
  
  .facets__sort select {
    padding: 0.5rem;
    border: 1px solid rgba(var(--color-foreground), 0.2);
    border-radius: 4px;
  }
  
  .facets__results-count {
    font-size: 0.875rem;
    color: rgba(var(--color-foreground), 0.7);
  }
</style>