{% comment %} Featured Collection Row (carousel, no deps) {% endcomment %}
<section class="featured-collection-row" data-section-id="{{ section.id }}">
  <div class="page-width">
    {% if section.settings.heading != blank %}<h2 class="h2">{{ section.settings.heading | escape }}</h2>{% endif %}
    {% assign coll = collections[section.settings.collection] %}
    {% if coll and coll.products_count > 0 %}
      <div class="row-slider js-snap-slider" role="region" aria-roledescription="carousel" aria-label="{{ section.settings.heading | default: 'Featured products' | escape }}">
        <div class="row-slider__track js-snap-track" tabindex="0" aria-live="polite">
          {% for p in coll.products limit: section.settings.limit %}
            <div class="row-slider__slide">
              {% render 'card-product', 
                product: p, 
                grid_position: forloop.index0, 
                columns: 4,
                show_swatches: section.settings.show_swatches,
                enable_hover_preview: section.settings.enable_hover_preview,
                max_swatches: section.settings.max_swatches,
                card_click_behavior: section.settings.card_click_behavior,
                show_quick_add: section.settings.show_quick_add,
                card_image_aspect_ratio: section.settings.card_image_aspect_ratio,
                enable_favorites: section.settings.enable_favorites,
                show_new_badges: section.settings.show_new_badges
              %}
            </div>
          {% endfor %}
        </div>
        <div class="row-slider__controls">
          <button class="row-slider__btn js-snap-prev" type="button" aria-label="Previous items">‹</button>
          <button class="row-slider__btn js-snap-next" type="button" aria-label="Next items">›</button>
        </div>
      </div>
    {% else %}
      {% if request.design_mode %}<p>No collection selected.</p>{% endif %}
    {% endif %}
  </div>

  <style>
    /* Display featured collection as a responsive grid (match collection grid behavior)
       Converted from slider to grid to avoid horizontal scrollbars and provide
       consistent presentation with other collection layouts. */
    .featured-collection-row { padding: 2.5rem 0; }

    .row-slider__track {
      display: grid;
      gap: 1rem;
      grid-template-columns: repeat(auto-fit, minmax(clamp(220px, 28vw, 320px), 1fr));
      align-items: start;
      margin: 0;
    }

    .row-slider__slide {
      /* Ensure slides behave like normal grid items */
      display: block;
      width: 100%;
    }

    /* Hide slider controls since grid is not interactive like a carousel */
    .row-slider__controls { display: none !important; }

    .row-slider__btn { display: none !important; }
  </style>
</section>

{% schema %}
{
  "name": "Featured collection row",
  "enabled_on": { "templates": ["collection"] },
  "settings": [
    { "type":"text", "id":"heading", "label":"Heading" },
    { "type":"collection", "id":"collection", "label":"Collection" },
    { "type":"range", "id":"limit", "label":"Products to show", "min": 4, "max": 24, "step": 1, "default": 12 },
    {
      "type": "header",
      "content": "Product card settings"
    },
    {
      "type": "checkbox",
      "id": "show_swatches",
      "label": "Show color swatches on product cards",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "enable_hover_preview",
      "label": "Enable card hover preview",
      "default": true
    },
    {
      "type": "range",
      "id": "max_swatches",
      "min": 3,
      "max": 8,
      "step": 1,
      "default": 5,
      "label": "Maximum swatches per product"
    },
    {
      "type": "select",
      "id": "card_click_behavior",
      "label": "Card click behavior",
      "options": [
        { "value": "link_with_variant", "label": "Link to product with selected variant" },
        { "value": "quick_add_variant", "label": "Quick add selected variant" }
      ],
      "default": "link_with_variant"
    },
    {
      "type": "checkbox",
      "id": "show_quick_add",
      "label": "Show quick add button",
      "default": false
    },
    {
      "type": "select",
      "id": "card_image_aspect_ratio",
      "label": "Card image aspect ratio",
      "options": [
        { "value": "3/4", "label": "Portrait (3:4)" },
        { "value": "1/1", "label": "Square (1:1)" },
        { "value": "4/3", "label": "Landscape (4:3)" }
      ],
      "default": "3/4"
    },
    {
      "type": "header",
      "content": "Product features"
    },
    {
      "type": "checkbox",
      "id": "enable_favorites",
      "label": "Enable favorites",
      "default": true,
      "info": "Show heart icon on product cards for saving favorites"
    },
    {
      "type": "checkbox",
      "id": "show_new_badges",
      "label": "Show NEW badges",
      "default": true,
      "info": "Display NEW badge on products created within the last 30 days"
    }
  ],
  "presets": [{ "name":"Featured collection row" }]
}
{% endschema %}
