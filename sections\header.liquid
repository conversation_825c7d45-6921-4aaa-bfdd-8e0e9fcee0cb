{%- liquid
  assign sticky_mode = section.settings.sticky_mode
  if settings.header_sticky_behavior and settings.header_sticky_behavior != 'inherit'
    assign sticky_mode = settings.header_sticky_behavior
  endif
-%}

<style>
  /* Critical pre-paint: if Gucci-style is enabled and the first content section is a hero,
     start header text/icons/logo in white immediately to avoid flash of black */
  body:has(#MainContent > .shopify-section:first-child .hero-banner) #SiteHeader[data-gucci-style="true"],
  body:has(#MainContent > .shopify-section:first-child .hero-split)  #SiteHeader[data-gucci-style="true"] {
    color: #fff;
  }
  body:has(#MainContent > .shopify-section:first-child .hero-banner) #SiteHeader[data-gucci-style="true"] a,
  body:has(#MainContent > .shopify-section:first-child .hero-banner) #SiteHeader[data-gucci-style="true"] .header__icon-link,
  body:has(#MainContent > .shopify-section:first-child .hero-banner) #SiteHeader[data-gucci-style="true"] .header__mobile-nav-link,
  body:has(#MainContent > .shopify-section:first-child .hero-banner) #SiteHeader[data-gucci-style="true"] .header__logo a,
  body:has(#MainContent > .shopify-section:first-child .hero-banner) #SiteHeader[data-gucci-style="true"] .header__nav a,
  body:has(#MainContent > .shopify-section:first-child .hero-split)  #SiteHeader[data-gucci-style="true"] a,
  body:has(#MainContent > .shopify-section:first-child .hero-split)  #SiteHeader[data-gucci-style="true"] .header__icon-link,
  body:has(#MainContent > .shopify-section:first-child .hero-split)  #SiteHeader[data-gucci-style="true"] .header__mobile-nav-link,
  body:has(#MainContent > .shopify-section:first-child .hero-split)  #SiteHeader[data-gucci-style="true"] .header__logo a,
  body:has(#MainContent > .shopify-section:first-child .hero-split)  #SiteHeader[data-gucci-style="true"] .header__nav a {
    color: #fff;
  }
  body:has(#MainContent > .shopify-section:first-child .hero-banner) #SiteHeader[data-gucci-style="true"] svg,
  body:has(#MainContent > .shopify-section:first-child .hero-split)  #SiteHeader[data-gucci-style="true"] svg {
    color: #fff; stroke: #fff; fill: none;
  }
  body:has(#MainContent > .shopify-section:first-child .hero-banner) #SiteHeader[data-gucci-style="true"] .header__hamburger-line,
  body:has(#MainContent > .shopify-section:first-child .hero-split)  #SiteHeader[data-gucci-style="true"] .header__hamburger-line {
    background-color: #fff;
  }
  body:has(#MainContent > .shopify-section:first-child .hero-banner) #SiteHeader[data-gucci-style="true"] .header__logo-set--light,
  body:has(#MainContent > .shopify-section:first-child .hero-split)  #SiteHeader[data-gucci-style="true"] .header__logo-set--light { display: inline-block; }
  body:has(#MainContent > .shopify-section:first-child .hero-banner) #SiteHeader[data-gucci-style="true"] .header__logo-set--default,
  body:has(#MainContent > .shopify-section:first-child .hero-split)  #SiteHeader[data-gucci-style="true"] .header__logo-set--default { display: none; }
  {% if template.name == 'index' %}
  /* Home template prelight: ensure white on first paint when Gucci-style is on */
  #SiteHeader[data-gucci-style="true"] { color: #fff !important; }
  #SiteHeader[data-gucci-style="true"] a,
  #SiteHeader[data-gucci-style="true"] .header__icon-link,
  #SiteHeader[data-gucci-style="true"] .header__mobile-nav-link,
  #SiteHeader[data-gucci-style="true"] .header__logo a,
  #SiteHeader[data-gucci-style="true"] .header__nav a,
  #SiteHeader[data-gucci-style="true"] .header__nav-arrow { color: #fff !important; }
  #SiteHeader[data-gucci-style="true"] svg { color: #fff !important; stroke: #fff !important; fill: none !important; }
  #SiteHeader[data-gucci-style="true"] .header__hamburger-line { background-color: #fff !important; }
  #SiteHeader[data-gucci-style="true"] .header__logo-set--light { display: inline-block !important; }
  #SiteHeader[data-gucci-style="true"] .header__logo-set--default { display: none !important; }
  /* Prelight gap fix for home hero */
  #MainContent .shopify-section:first-child { margin-top: 0 !important; }
  {% endif %}
</style>

<header 
  id="SiteHeader" 
  class="header site-header" 
  data-section-type="header" 
  data-section-id="{{ section.id }}"
  data-sticky-mode="{{ sticky_mode }}"
  data-sticky-breakpoint="{{ section.settings.sticky_breakpoint }}"
  data-reveal-threshold="{{ section.settings.reveal_threshold }}"
  data-animation-ms="{{ section.settings.animation_ms }}"
  data-shadow-when-stuck="{{ section.settings.shadow_when_stuck }}"
  data-shadow-strength="{{ section.settings.shadow_strength }}"
  data-solid-background="{{ section.settings.solid_background_when_stuck }}"
  data-padding-reduction="{{ section.settings.padding_reduction }}"
  data-respect-reduced-motion="{{ section.settings.respect_reduced_motion }}"
  data-overlay-on-hero="{{ section.settings.overlay_on_hero }}"
  data-gucci-style="{{ section.settings.gucci_style_behavior }}"
  data-glass-effect="{{ section.settings.glass_effect }}"
  data-gucci-range="180"
  style="--logo-font-size: {{ section.settings.logo_font_size | default: 20 }}px; --header-z: {{ section.settings.z_index | default: 100 }};">
  
  <a class="skip-link" href="#MainContent">Skip to content</a>
  <div class="header__inner">
    <h1 class="header__logo">
      <a href="{{ routes.root_url }}" class="header__logo-link" title="{{ shop.name }}">
        {% if section.settings.logo or section.settings.logo_light %}
          {% if section.settings.logo %}
            <span class="header__logo-set header__logo-set--default">
              <picture>
                {% if section.settings.logo_mobile %}
                  <source srcset="{{ section.settings.logo_mobile | image_url: width: section.settings.logo_width }}" media="(max-width: 599px)">
                {% endif %}
                <img src="{{ section.settings.logo | image_url: width: section.settings.logo_width }}"
                     alt="{{ shop.name }}"
                     width="{{ section.settings.logo_width }}"
                     height="auto"
                     loading="lazy">
              </picture>
            </span>
          {% endif %}

          {% if section.settings.logo_light %}
            <span class="header__logo-set header__logo-set--light">
              <picture>
                {% if section.settings.logo_mobile_light %}
                  <source srcset="{{ section.settings.logo_mobile_light | image_url: width: section.settings.logo_width }}" media="(max-width: 599px)">
                {% endif %}
                <img src="{{ section.settings.logo_light | image_url: width: section.settings.logo_width }}"
                     alt="{{ shop.name }}"
                     width="{{ section.settings.logo_width }}"
                     height="auto"
                     loading="lazy">
              </picture>
            </span>
          {% endif %}
        {% else %}
          {{ shop.name }}
        {% endif %}
      </a>
    </h1>

    <nav class="header__nav" role="navigation" aria-label="Main navigation">
      <!-- Mobile Menu Toggle -->
      <button type="button" class="header__mobile-menu-toggle" aria-expanded="false" aria-controls="mobile-menu" aria-label="Open menu">
        <span class="header__hamburger">
          <span class="header__hamburger-line"></span>
          <span class="header__hamburger-line"></span>
          <span class="header__hamburger-line"></span>
        </span>
      </button>

      <!-- Desktop Navigation -->
      <ul class="header__nav-list header__nav-list--desktop">
        {% comment %}First try blocks (new system){% endcomment %}
        {% assign has_blocks = false %}
        {% for block in section.blocks %}
          {% assign has_blocks = true %}
          {% case block.type %}
            {% when 'simple_link' %}
              {% assign menu = block.settings.menu %}
              {% if menu != blank and menu.links.size > 0 %}
                <li class="header__nav-item">
                  <div class="header__nav-link-wrapper" {{ block.shopify_attributes }}>
                    {% if menu.links.size == 1 %}
                      {%- comment -%} Single link - no dropdown {%- endcomment -%}
                      <a href="{{ menu.links.first.url }}" class="header__nav-link">
                        {{ menu.title }}
                      </a>
                    {% else %}
                      {%- comment -%} Multiple links - show dropdown {%- endcomment -%}
                      <a href="{{ menu.links.first.url }}" class="header__nav-link header__nav-link--dropdown" aria-expanded="false" aria-haspopup="true">
                        {{ menu.title }}
                        <span class="header__nav-arrow" aria-hidden="true">▼</span>
                      </a>
                      <ul class="header__dropdown">
                        {% for link in menu.links %}
                          <li class="header__dropdown-item">
                            <a href="{{ link.url }}" class="header__dropdown-link">{{ link.title }}</a>
                          </li>
                        {% endfor %}
                      </ul>
                    {% endif %}
                  </div>
                </li>
              {% elsif menu != blank %}
                {%- comment -%} Menu exists but has no links - show as simple link {%- endcomment -%}
                <li class="header__nav-item">
                  <div class="header__nav-link-wrapper" {{ block.shopify_attributes }}>
                    <a href="#" class="header__nav-link">{{ menu.title | default: 'Menu' }}</a>
                  </div>
                </li>
              {% endif %}

            {% when 'megamenu' %}
              <li class="header__nav-item">
                <div class="header__nav-link-wrapper" {{ block.shopify_attributes }}>
                  <a href="#" class="header__nav-link header__nav-link--megamenu" aria-expanded="false" aria-haspopup="true">
                    {{ block.settings.title | default: 'Shop' }}
                    <span class="header__nav-arrow" aria-hidden="true">▼</span>
                  </a>
                  <div class="header__megamenu">
                    <div class="header__megamenu-content">
                      <div class="header__megamenu-columns">
                        {% assign has_any_menu = false %}
                        {% for i in (1..3) %}
                          {% assign menu_key = 'menu_' | append: i %}
                          {% assign menu = block.settings[menu_key] %}
                          {% if menu != blank and menu.links.size > 0 %}
                            {% assign has_any_menu = true %}
                            <div class="header__megamenu-column">
                              <h3 class="header__megamenu-heading">{{ menu.title }}</h3>
                              <ul class="header__megamenu-links">
                                {% for link in menu.links %}
                                  <li class="header__megamenu-item">
                                    <a href="{{ link.url }}" class="header__megamenu-link">{{ link.title }}</a>
                                  </li>
                                {% endfor %}
                              </ul>
                            </div>
                          {% endif %}
                        {% endfor %}
                        
                        {% unless has_any_menu %}
                          {%- comment -%} Show placeholder content when no menus are configured {%- endcomment -%}
                          <div class="header__megamenu-column">
                            <h3 class="header__megamenu-heading">Categories</h3>
                            <ul class="header__megamenu-links">
                              <li class="header__megamenu-item">
                                <a href="#" class="header__megamenu-link">Configure in Theme Editor</a>
                              </li>
                            </ul>
                          </div>
                        {% endunless %}
                      </div>

                      {% if block.settings.promo_image != blank or block.settings.promo_heading != blank %}
                        <div class="header__megamenu-promo">
                          {% if block.settings.promo_image %}
                            {% if block.settings.promo_link != blank %}
                              <a href="{{ block.settings.promo_link }}" class="header__megamenu-promo-link">
                            {% endif %}
                            <img src="{{ block.settings.promo_image | image_url: width: 300 }}" 
                                 alt="{{ block.settings.promo_heading | default: 'Promotion' }}" 
                                 width="300" 
                                 height="auto"
                                 loading="lazy"
                                 class="header__megamenu-promo-image">
                            {% if block.settings.promo_link != blank %}
                              </a>
                            {% endif %}
                          {% endif %}
                          
                          {% if block.settings.promo_heading != blank %}
                            <div class="header__megamenu-promo-content">
                              {% if block.settings.promo_link != blank %}
                                <a href="{{ block.settings.promo_link }}" class="header__megamenu-promo-heading-link">
                              {% endif %}
                              <h4 class="header__megamenu-promo-heading">{{ block.settings.promo_heading }}</h4>
                              {% if block.settings.promo_link != blank %}
                                </a>
                              {% endif %}
                            </div>
                          {% endif %}
                        </div>
                      {% endif %}
                    </div>
                  </div>
                </div>
              </li>
          {% endcase %}
        {% endfor %}
        
        {% comment %}Fallback to section menu setting if no blocks{% endcomment %}
        {% unless has_blocks %}
          {% if section.settings.menu != blank and section.settings.menu.links.size > 0 %}
            {% for link in section.settings.menu.links %}
              <li class="header__nav-item">
                {% if link.links != blank and link.links.size > 0 %}
                  {%- comment -%} Link has dropdown menu {%- endcomment -%}
                  <div class="header__nav-link-wrapper">
                    <a href="{{ link.url }}" class="header__nav-link header__nav-link--dropdown" aria-expanded="false" aria-haspopup="true">
                      {{ link.title }}
                      <span class="header__nav-arrow" aria-hidden="true">▼</span>
                    </a>
                    <ul class="header__dropdown">
                      {% for child_link in link.links %}
                        <li class="header__dropdown-item">
                          <a href="{{ child_link.url }}" class="header__dropdown-link">{{ child_link.title }}</a>
                        </li>
                      {% endfor %}
                    </ul>
                  </div>
                {% else %}
                  {%- comment -%} Simple single link {%- endcomment -%}
                  <div class="header__nav-link-wrapper">
                    <a href="{{ link.url }}" class="header__nav-link">{{ link.title }}</a>
                  </div>
                {% endif %}
              </li>
            {% endfor %}
          {% endif %}
        {% endunless %}
      </ul>

      <!-- Mobile Navigation -->
      <div class="header__mobile-menu" id="mobile-menu" aria-hidden="true">
        <div class="header__mobile-menu-inner">
          <ul class="header__mobile-nav-list">
            {% comment %}Mobile menu content - same logic as desktop{% endcomment %}
            {% assign has_mobile_blocks = false %}
            {% for block in section.blocks %}
              {% assign has_mobile_blocks = true %}
              {% case block.type %}
                {% when 'simple_link' %}
                  {% assign menu = block.settings.menu %}
                  {% if menu != blank and menu.links.size > 0 %}
                    {% for link in menu.links %}
                      <li class="header__mobile-nav-item">
                        {% if link.links != blank and link.links.size > 0 %}
                          {%- comment -%} Link has dropdown menu {%- endcomment -%}
                          <details class="header__mobile-dropdown">
                            <summary class="header__mobile-nav-link">
                              {{ link.title }}
                              <span class="header__mobile-nav-arrow" aria-hidden="true">▼</span>
                            </summary>
                            <ul class="header__mobile-dropdown-list">
                              {% for child_link in link.links %}
                                <li class="header__mobile-dropdown-item">
                                  <a href="{{ child_link.url }}" class="header__mobile-dropdown-link">{{ child_link.title }}</a>
                                </li>
                              {% endfor %}
                            </ul>
                          </details>
                        {% else %}
                          {%- comment -%} Simple single link {%- endcomment -%}
                          <a href="{{ link.url }}" class="header__mobile-nav-link">{{ link.title }}</a>
                        {% endif %}
                      </li>
                    {% endfor %}
                  {% elsif menu != blank %}
                    <li class="header__mobile-nav-item">
                      <a href="#" class="header__mobile-nav-link">{{ menu.title | default: 'Menu' }}</a>
                    </li>
                  {% endif %}

                {% when 'megamenu' %}
                  <li class="header__mobile-nav-item">
                    <details class="header__mobile-dropdown">
                      <summary class="header__mobile-nav-link">
                        {{ block.settings.title | default: 'Shop' }}
                        <span class="header__mobile-nav-arrow" aria-hidden="true">▼</span>
                      </summary>
                      <div class="header__mobile-megamenu">
                        {% for i in (1..3) %}
                          {% assign menu_key = 'menu_' | append: i %}
                          {% assign menu = block.settings[menu_key] %}
                          {% if menu != blank and menu.links.size > 0 %}
                            <div class="header__mobile-megamenu-column">
                              <h4 class="header__mobile-megamenu-heading">{{ menu.title }}</h4>
                              <ul class="header__mobile-megamenu-links">
                                {% for link in menu.links %}
                                  <li class="header__mobile-megamenu-item">
                                    <a href="{{ link.url }}" class="header__mobile-megamenu-link">{{ link.title }}</a>
                                  </li>
                                {% endfor %}
                              </ul>
                            </div>
                          {% endif %}
                        {% endfor %}
                      </div>
                    </details>
                  </li>
              {% endcase %}
            {% endfor %}
            
            {% comment %}Mobile fallback menu{% endcomment %}
            {% unless has_mobile_blocks %}
              {% if section.settings.menu != blank and section.settings.menu.links.size > 0 %}
                {% for link in section.settings.menu.links %}
                  <li class="header__mobile-nav-item">
                    {% if link.links != blank and link.links.size > 0 %}
                      <details class="header__mobile-dropdown">
                        <summary class="header__mobile-nav-link">
                          {{ link.title }}
                          <span class="header__mobile-nav-arrow" aria-hidden="true">▼</span>
                        </summary>
                        <ul class="header__mobile-dropdown-list">
                          {% for child_link in link.links %}
                            <li class="header__mobile-dropdown-item">
                              <a href="{{ child_link.url }}" class="header__mobile-dropdown-link">{{ child_link.title }}</a>
                            </li>
                          {% endfor %}
                        </ul>
                      </details>
                    {% else %}
                      <a href="{{ link.url }}" class="header__mobile-nav-link">{{ link.title }}</a>
                    {% endif %}
                  </li>
                {% endfor %}
              {% endif %}
            {% endunless %}
          </ul>
        </div>
      </div>
    </nav>

    <div class="header__icons">
      {% if shop.customer_accounts_enabled %}
        <a href="{{ routes.account_url }}" class="header__icon-link" aria-label="Account">
          {{ 'icon-account.svg' | inline_asset_content }}
        </a>
      {% endif %}

      <button type="button" class="header__icon-link header__cart-link" 
              aria-label="Open cart" 
              aria-controls="cart-drawer" 
              aria-expanded="false" 
              data-open-cart>
        {% if cart.item_count > 0 %}
          <span class="header__cart-count" aria-label="{{ cart.item_count }} {{ 'general.cart.item_count' | t }}">{{ cart.item_count }}</span>
        {% endif %}
        {{ 'icon-cart.svg' | inline_asset_content }}
      </button>
    </div>
  </div>
</header>

<div class="sticky-sentinel" aria-hidden="true"></div>

<style>
  .header {
    position: relative;
    z-index: var(--header-z, 100);
    transition: transform var(--header-anim, 150ms) ease, box-shadow 120ms ease, background-color 120ms ease;
    will-change: transform;
    width: 100%;
    overflow: hidden;
  }
  
  /* Ensure header is properly positioned for sticky behavior */
  .header.site-header {
    position: relative;
    z-index: var(--header-z, 100);
  }
  
  /* Ensure header is properly positioned for always sticky mode */
  .header.is-sticky-always {
    position: sticky;
    top: 0;
    z-index: var(--header-z, 100);
  }
  
  /* Ensure header section has proper padding */
  .shopify-section-group-header-group .header {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  /* Sticky Header Styles */
  .site-header {
    position: relative;
    z-index: var(--header-z, 100);
    transition: transform var(--header-anim, 150ms) ease, box-shadow 120ms ease, background-color 120ms ease;
    will-change: transform;
  }

  /* Overlay mode: header floats on top of content */
  body.header--overlay .header a,
  body.header--overlay .header .header__icon-link,
  body.header--overlay .header .header__mobile-nav-link { color: #fff; }
  body.header--overlay .header svg { color: currentColor; }
  body.header--overlay .site-header { background: transparent; }

  /* Black header on pages mode: override overlay styles for non-home pages */
  body.header--black-on-pages .header {
    background: #fff !important;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
    color: #000 !important;
    /* Do not override sticky positioning here */
  }
  body.header--black-on-pages .header a,
  body.header--black-on-pages .header .header__icon-link,
  body.header--black-on-pages .header .header__mobile-nav-link {
    color: #000 !important;
  }
  body.header--black-on-pages .header svg {
    color: currentColor !important;
  }
  body.header--black-on-pages .site-header {
    background: #fff !important;
  }
  body.header--black-on-pages .header__hamburger-line {
    background-color: #000 !important;
  }
  body.header--black-on-pages .header__mobile-menu-toggle:hover .header__hamburger-line {
    background-color: #333 !important;
  }

  /* Always mode: fixed */
[data-sticky-mode="always"] .site-header,
  [data-sticky-mode="always"].site-header,
  body.header--black-on-pages [data-sticky-mode="always"] .site-header,
  body.header--black-on-pages [data-sticky-mode="always"].site-header,
  .header.is-sticky-always {
    position: fixed;
    top: var(--announcement-height, 0px);
    left: 0; right: 0;
    width: 100%;
    z-index: var(--header-z, 100);
  }
  
  /* Ensure always sticky mode works properly */
  .site-header[data-sticky-mode="always"],
  body.header--black-on-pages .site-header[data-sticky-mode="always"] {
    position: fixed;
    top: var(--announcement-height, 0px);
    left: 0;
    right: 0;
    width: 100%;
    z-index: var(--header-z, 100);
  }
  
  /* Ensure header is properly positioned for always sticky mode */
  .site-header.is-sticky-always,
  body.header--black-on-pages .site-header.is-sticky-always {
    position: fixed;
    top: var(--announcement-height, 0px);
    left: 0;
    right: 0;
    width: 100%;
    z-index: var(--header-z, 100);
  }

  /* Solid background when sticky - only for always mode now */
  .site-header.is-sticky-always[data-solid-background="true"] {
    background: var(--color-background, #fff) !important;
  }

  /* Padding reduction when sticky - only for always mode now */
  .site-header.is-sticky-always[data-padding-reduction]:not([data-padding-reduction="0"]) .header__inner {
    padding-top: calc(0.5rem - var(--padding-reduction, 0px));
    padding-bottom: calc(0.5rem - var(--padding-reduction, 0px));
  }

  /* Anchor targets land below header */
  html { 
    scroll-padding-top: var(--header-h, 0px); 
  }

  /* Respect reduced motion */
  @media (prefers-reduced-motion: reduce) {
    [data-respect-reduced-motion="true"] .site-header { 
      transition: none; 
    }
  }

  /* Compute and expose header height as --header-h variable */
  body:has(.site-header) {
    /* fallback value; JS will set accurate value inline on load */
    --header-h: 64px;
  }

  /* Sentinel - only has height when header is actually fixed */
  .sticky-sentinel {
    height: 0;
    position: relative;
  }

  /* Default: Sentinel gets height when header is sticky (provides spacing) */
  body:has(.site-header.is-sticky-always) .sticky-sentinel {
    height: calc(var(--header-h, 64px) + var(--announcement-height, 0px));
  }


  /* Hide sentinel when in natural flow mode (sticky: none) */
  body.header--natural-flow .sticky-sentinel {
    height: 0 !important;
    display: none;
  }

  /* Natural flow mode: header stays in normal flow with no extra margins */
  body.header--natural-flow .header {
    position: relative;
    z-index: 100; /* Lower than announcement bar (200) */
    margin-top: 0;
  }

  /* Unified overlay header background with variable control */
  body.header--overlay .header,
  body.header--hero-overlay .header {
    background: rgba(255, 255, 255, var(--header-bg-alpha, 0)) !important;
    backdrop-filter: var(--header-blur, none);
    -webkit-backdrop-filter: var(--header-blur, none);
  }

  /* Highest-certainty background for the fade (only touches background) */
  body.header--hero-overlay .header.site-header {
    background: rgba(255, 255, 255, var(--header-bg-alpha, 0)) !important;
    backdrop-filter: var(--header-blur, none);
    -webkit-backdrop-filter: var(--header-blur, none);
  }

  /* Nuclear-strength selector - wins against any competing background rules */
  body.header--hero-overlay header#SiteHeader.header {
    background: rgba(255, 255, 255, var(--header-bg-alpha, 0)) !important;
    backdrop-filter: var(--header-blur, none);
    -webkit-backdrop-filter: var(--header-blur, none);
  }

  /* Enable var-driven background whenever Gucci is on */
  body.gucci-enabled header#SiteHeader.header {
    background: rgba(255,255,255,var(--header-bg-alpha,0)) !important;
    backdrop-filter: var(--header-blur, none);
    -webkit-backdrop-filter: var(--header-blur, none);
  }

  /* Fallback for backdrop-filter support and ensure positioning */
  body.gucci-enabled header#SiteHeader {
    position: relative; /* Ensure backdrop-filter context */
    background: rgba(255,255,255,var(--header-bg-alpha,0)) !important;
    backdrop-filter: var(--header-blur, none);
    -webkit-backdrop-filter: var(--header-blur, none);
  }

  /* Most specific selector - nuclear option */
  body.gucci-enabled #SiteHeader {
    backdrop-filter: var(--header-blur, none) !important;
    -webkit-backdrop-filter: var(--header-blur, none) !important;
  }

  /* Prevent the "black on pages" override from killing the fade */
  body.header--black-on-pages:not(.gucci-enabled) .header {
    background: #fff !important;
  }

  /* Hero overlay mode: header floats over hero sections */
  body.header--hero-overlay .header {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    z-index: 150; /* Above hero content but below announcement bar */
    color: white;
    box-shadow: none !important;
  }

  body.header--hero-overlay .header a,
  body.header--hero-overlay .header .header__icon-link,
  body.header--hero-overlay .header .header__mobile-nav-link {
    color: white;
  }

  body.header--hero-overlay .header svg {
    color: currentColor;
  }

  body.header--hero-overlay .header__hamburger-line {
    background-color: white !important;
  }

  body.header--hero-overlay .header__mobile-menu-toggle:hover .header__hamburger-line {
    background-color: rgba(255, 255, 255, 0.8) !important;
  }

  /* Hide sentinel when in hero overlay mode */
  body.header--hero-overlay .sticky-sentinel {
    height: 0 !important;
    display: none;
  }

  /* Remove gap between header and hero */
  body.header--hero-overlay .main-content > .shopify-section:first-child {
    margin-top: 0 !important;
  }

  /* --- Gucci fade overlay (variable-driven) --- */
  .site-header {
    /* overlay background driven by JS-set alpha var */
    background-color: rgba(255,255,255,var(--header-bg-alpha, 0));
    /* optional nice glass look while still "white": toggle via JS when > 0 */
    backdrop-filter: var(--header-blur, none);
    -webkit-backdrop-filter: var(--header-blur, none);
    transition:
      background-color 180ms ease,
      color 180ms ease,
      box-shadow 180ms ease;
  }

  /* Start transparent + light (over hero) */
  .header.theme--light-on-hero,
  body.header--hero-overlay .header:not(.theme--dark-over-content) {
    color: #fff;
  }
  .header.theme--light-on-hero a,
  .header.theme--light-on-hero .header__icon-link,
  .header.theme--light-on-hero .header__mobile-nav-link,
  .header.theme--light-on-hero .header__logo a,
  .header.theme--light-on-hero .header__nav a,
  .header.theme--light-on-hero svg {
    color: #fff !important;
  }
  .header.theme--light-on-hero svg { stroke: #fff; fill: none; }
  .header.theme--light-on-hero .header__hamburger-line { background-color: #fff !important; }

  /* When overlay gets sufficiently opaque, flip to dark UI */
  .header.theme--dark-over-content {
    color: #000 !important;
  }
  .header.theme--dark-over-content a,
  .header.theme--dark-over-content .header__icon-link,
  .header.theme--dark-over-content .header__mobile-nav-link,
  .header.theme--dark-over-content .header__logo a,
  .header.theme--dark-over-content .header__nav a {
    color: #000 !important;
  }
  .header.theme--dark-over-content svg {
    stroke: #000 !important;
    fill: none !important;
  }
  .header.theme--dark-over-content .header__hamburger-line { 
    background-color: #000 !important; 
  }

  /* Preload CSS path: If Gucci-style is enabled and the first section is a hero,
     start the header in a white theme immediately (no JS required) */
  body:has(#MainContent > .shopify-section:first-child .hero-banner) #SiteHeader[data-gucci-style="true"],
  body:has(#MainContent > .shopify-section:first-child .hero-split)  #SiteHeader[data-gucci-style="true"] {
    color: #fff;
  }
  body:has(#MainContent > .shopify-section:first-child .hero-banner) #SiteHeader[data-gucci-style="true"] a,
  body:has(#MainContent > .shopify-section:first-child .hero-banner) #SiteHeader[data-gucci-style="true"] .header__icon-link,
  body:has(#MainContent > .shopify-section:first-child .hero-banner) #SiteHeader[data-gucci-style="true"] .header__mobile-nav-link,
  body:has(#MainContent > .shopify-section:first-child .hero-banner) #SiteHeader[data-gucci-style="true"] .header__logo a,
  body:has(#MainContent > .shopify-section:first-child .hero-banner) #SiteHeader[data-gucci-style="true"] .header__nav a,
  body:has(#MainContent > .shopify-section:first-child .hero-banner) #SiteHeader[data-gucci-style="true"] .header__nav-arrow,
  body:has(#MainContent > .shopify-section:first-child .hero-split)  #SiteHeader[data-gucci-style="true"] a,
  body:has(#MainContent > .shopify-section:first-child .hero-split)  #SiteHeader[data-gucci-style="true"] .header__icon-link,
  body:has(#MainContent > .shopify-section:first-child .hero-split)  #SiteHeader[data-gucci-style="true"] .header__mobile-nav-link,
  body:has(#MainContent > .shopify-section:first-child .hero-split)  #SiteHeader[data-gucci-style="true"] .header__logo a,
  body:has(#MainContent > .shopify-section:first-child .hero-split)  #SiteHeader[data-gucci-style="true"] .header__nav a,
  body:has(#MainContent > .shopify-section:first-child .hero-split)  #SiteHeader[data-gucci-style="true"] .header__nav-arrow {
    color: #fff !important;
  }
  body:has(#MainContent > .shopify-section:first-child .hero-banner) #SiteHeader[data-gucci-style="true"] svg,
  body:has(#MainContent > .shopify-section:first-child .hero-split)  #SiteHeader[data-gucci-style="true"] svg {
    color: #fff !important; stroke: #fff !important; fill: none !important;
  }
  body:has(#MainContent > .shopify-section:first-child .hero-banner) #SiteHeader[data-gucci-style="true"] .header__hamburger-line,
  body:has(#MainContent > .shopify-section:first-child .hero-split)  #SiteHeader[data-gucci-style="true"] .header__hamburger-line {
    background-color: #fff !important;
  }
  /* Swap logo sets (light vs default) before JS runs */
  body:has(#MainContent > .shopify-section:first-child .hero-banner) #SiteHeader[data-gucci-style="true"] .header__logo-set--light,
  body:has(#MainContent > .shopify-section:first-child .hero-split)  #SiteHeader[data-gucci-style="true"] .header__logo-set--light { display: inline-block !important; }
  body:has(#MainContent > .shopify-section:first-child .hero-banner) #SiteHeader[data-gucci-style="true"] .header__logo-set--default,
  body:has(#MainContent > .shopify-section:first-child .hero-split)  #SiteHeader[data-gucci-style="true"] .header__logo-set--default { display: none !important; }
  /* Preload gap fix when hero is first */
  body:has(#MainContent > .shopify-section:first-child .hero-banner) .sticky-sentinel,
  body:has(#MainContent > .shopify-section:first-child .hero-split)  .sticky-sentinel { height: 0 !important; }
  body:has(#MainContent > .shopify-section:first-child .hero-banner) #MainContent .shopify-section:first-child,
  body:has(#MainContent > .shopify-section:first-child .hero-split)  #MainContent .shopify-section:first-child { margin-top: 0 !important; }

  /* When fully past hero, add a subtle shadow */
  .header.is-fixed-over-content {
    box-shadow: 0 2px 6px rgba(0,0,0,0.08);
  }

  /* Keep overlay header pinned below announcement bar when needed */
  body.header--hero-overlay .header {
    top: var(--announcement-height, 0px);
  }

  /* Skip link for accessibility */
  .skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: #000;
    color: #fff;
    padding: 8px;
    text-decoration: none;
    z-index: 9999;
    transition: top 0.3s;
  }

  .skip-link:focus {
    top: 6px;
  }

  /* Ensure main content doesn't overlap with fixed header */
  .main-content {
    transition: padding-top var(--header-anim, 150ms) ease;
  }
  /* Push content below fixed header (except when overlay header is enabled) */
  /* We use a spacer element (sticky-sentinel) instead of padding the main content
     to prevent cut-off and layout thrash with various templates. */

  /* Seam fixer: overlap header by 1px to avoid sub-pixel gap with announcement bar */
  .site-header::before {
    content: "";
    position: absolute;
    top: -1px;
    left: 0;
    right: 0;
    height: 1px;
    background: inherit;
    pointer-events: none;
  }

  .header__inner {
    position: relative; /* ensure absolute children align to inner padding */
    height: auto;
    min-height: 4rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%; /* Use full width of the padded section container */
    padding: 0.5rem 0; /* Vertical padding only - horizontal handled by section wrapper */
    gap: 1rem;
    flex-wrap: nowrap;
    max-width: 100%; /* Ensure it doesn't exceed container width */
  }
  
  /* Ensure header inner respects section padding */
  .shopify-section-group-header-group .header__inner {
    width: 100%; /* Use full width */
    margin: 0 auto;
    max-width: 100%; /* Use full width */
    padding-left: 0;
    padding-right: 0;
  }

  /* Logo */
  .header__logo {
    margin: 0;
    font-size: inherit;
    font-weight: inherit;
    flex-shrink: 0;
  }
  .header__logo-link {
    display: block;
    text-decoration: none;
    color: var(--color-foreground);
    font-family: var(--font-primary--family);
    font-weight: bold;
    font-size: var(--logo-font-size, 32px);
    text-transform: uppercase;
    letter-spacing: 0.5em;
    transition: opacity 0.2s ease;
  }
  .header__logo-link:hover {
    opacity: 0.8;
  }
  .header__logo-link img {
    display: block;
    height: auto;
    max-height: calc(4rem - 1rem);
  }
  /* Swap logo variants based on theme state */
  .header__logo-set { display: inline-block; line-height: 0; }
  .header__logo-set--light { display: none; }
  /* Over hero (start): use light logo when available */
  .header.theme--light-on-hero .header__logo-set--light { display: inline-block; }
  .header.theme--light-on-hero .header__logo-set--default { display: none; }
  /* After flip / non-hero pages: use default */
  .header.theme--dark-over-content .header__logo-set--default { display: inline-block; }
  .header.theme--dark-over-content .header__logo-set--light { display: none; }

  /* Navigation */
  .header__nav {
    flex: 1;
    margin: 0 1rem;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
  }
  .header__nav-list {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1.5rem;
    list-style: none;
    margin: 0;
    padding: 0;
    flex-wrap: nowrap;
    white-space: nowrap;
  }
  .header__nav-item {
    position: relative;
    flex-shrink: 0;
  }
  .header__nav-link-wrapper {
    position: relative;
  }
  .header__nav-link {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    padding: 1rem 0;
    text-decoration: none;
    color: var(--color-foreground);
    font-weight: 400;
    font-size: 0.875em;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    transition: color 0.2s ease;
    cursor: pointer;
  }
  .header__nav-link:hover {
    color: var(--color-foreground-75);
  }
  .header__nav-arrow {
    font-size: 0.75rem;
    transition: transform 0.2s ease;
  }
  .header__nav-link[aria-expanded="true"] .header__nav-arrow {
    transform: rotate(180deg);
  }

  /* Simple Dropdown */
  .header__dropdown {
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    min-width: 200px;
    background: white;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    list-style: none;
    margin: 0;
    padding: 0.5rem 0;
    opacity: 0;
    visibility: hidden;
    transform: translateX(-50%) translateY(-10px);
    transition: all 0.2s ease;
    z-index: 1000;
  }
  .header__nav-link-wrapper:hover .header__dropdown,
  .header__nav-link-wrapper.active .header__dropdown {
    opacity: 1;
    visibility: visible;
    transform: translateX(-50%) translateY(0);
  }
  .header__dropdown-item {
    margin: 0;
  }
  .header__dropdown-link {
    display: block;
    padding: 0.5rem 1rem;
    text-decoration: none;
    color: var(--color-foreground);
    font-size: 0.9rem;
    transition: background-color 0.2s ease;
  }
  .header__dropdown-link:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }

  /* Megamenu */
  .header__megamenu {
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    width: 800px;
    max-width: 90vw;
    background: white;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    opacity: 0;
    visibility: hidden;
    transform: translateX(-50%) translateY(-10px);
    transition: all 0.2s ease;
    z-index: 1000;
  }
  .header__nav-link-wrapper:hover .header__megamenu,
  .header__nav-link-wrapper.active .header__megamenu {
    opacity: 1;
    visibility: visible;
    transform: translateX(-50%) translateY(0);
  }
  .header__megamenu-content {
    display: flex;
    padding: 2rem;
    gap: 2rem;
  }
  .header__megamenu-columns {
    display: flex;
    flex: 1;
    gap: 2rem;
  }
  .header__megamenu-column {
    flex: 1;
  }
  .header__megamenu-heading {
    margin: 0 0 1rem 0;
    font-size: 1rem;
    font-weight: 600;
    color: var(--color-foreground);
  }
  .header__megamenu-links {
    list-style: none;
    margin: 0;
    padding: 0;
  }
  .header__megamenu-item {
    margin: 0;
  }
  .header__megamenu-link {
    display: block;
    padding: 0.25rem 0;
    text-decoration: none;
    color: var(--color-foreground-75);
    font-size: 0.9rem;
    transition: color 0.2s ease;
  }
  .header__megamenu-link:hover {
    color: var(--color-foreground);
  }

  /* Megamenu Promotion */
  .header__megamenu-promo {
    width: 250px;
    flex-shrink: 0;
  }
  .header__megamenu-promo-image {
    width: 100%;
    height: auto;
    border-radius: 4px;
    margin-bottom: 1rem;
  }
  .header__megamenu-promo-heading {
    margin: 0;
    font-size: 1rem;
    font-weight: 600;
    color: var(--color-foreground);
  }
  .header__megamenu-promo-link,
  .header__megamenu-promo-heading-link {
    text-decoration: none;
    color: inherit;
  }
  .header__megamenu-promo-link:hover,
  .header__megamenu-promo-heading-link:hover {
    opacity: 0.8;
  }

  /* Icons */
  .header__icons {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    flex-shrink: 0;
  }
  .header__icon-link {
    position: relative;
    display: flex;
    align-items: center;
    text-decoration: none;
    color: var(--color-foreground);
    transition: color 0.2s ease;
  }
  .header__icon-link:hover {
    color: var(--color-foreground-75);
  }
  .header__icon-link svg {
    width: 1.5rem;
    height: 1.5rem;
  }
  .header__cart-count {
    position: absolute;
    top: 0;
    right: 0;
    background: var(--color-foreground);
    color: var(--color-background);
    border-radius: 50%;
    min-width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    font-weight: 600;
    line-height: 1;
  }

  .header__cart-link {
    background: none;
    border: none;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: background-color 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .header__cart-link:hover {
    background: rgba(0, 0, 0, 0.05);
  }

  /* Mobile Responsive */
  @media (max-width: 999px) {
    .header__inner {
      height: 3rem;
    }
    .header__logo-link img {
      max-height: calc(3rem - 0.5rem);
    }
    .header__nav {
      display: flex; /* Changed from none to flex for testing */
    }
    .header__nav-list {
      gap: 1rem;
    }
  }

  @media (max-width: 599px) {
    .header__inner {
      padding: 0.25rem 0; /* Reduced padding for tighter spacing */
    }
    .header__icons {
      gap: 0.25rem;
    }
  }

  /* Mobile Menu Toggle Button */
  .header__mobile-menu-toggle {
    display: none;
    background: none;
    border: none;
    padding: 0.5rem;
    cursor: pointer;
    z-index: 1001;
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
  }

  .header__hamburger {
    display: flex;
    flex-direction: column;
    width: 20px;
    height: 15px;
    justify-content: space-between;
  }

  .header__hamburger-line {
    width: 100%;
    height: 2px;
    background-color: #333;
    transition: all 0.3s ease;
    transform-origin: center;
  }

  .header__mobile-menu-toggle:hover .header__hamburger-line {
    background-color: #000;
  }

  /* Hamburger animation when menu is open */
  .header__mobile-menu-toggle.active .header__hamburger-line:nth-child(1) {
    transform: rotate(45deg) translate(6px, 6px);
  }

  .header__mobile-menu-toggle.active .header__hamburger-line:nth-child(2) {
    opacity: 0;
  }

  .header__mobile-menu-toggle.active .header__hamburger-line:nth-child(3) {
    transform: rotate(-45deg) translate(6px, -6px);
  }

  /* Mobile Navigation Menu */
  .header__mobile-menu {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
    z-index: 1000;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
    opacity: 0;
    visibility: hidden;
    pointer-events: none;
  }

  .header__mobile-menu.active {
    transform: translateX(0);
    opacity: 1;
    visibility: visible;
  }

  .header__mobile-menu-inner {
    width: 80%;
    max-width: 400px;
    height: 100%;
    background: white;
    padding: 2rem 1rem;
    overflow-y: auto;
    transform: translateX(-100%);
    transition: transform 0.3s ease 0.1s;
  }

  .header__mobile-menu.active .header__mobile-menu-inner {
    transform: translateX(0);
  }

  .header__mobile-nav-list {
    list-style: none;
    margin: 0;
    padding: 0;
  }

  .header__mobile-nav-item {
    border-bottom: 1px solid #eee;
  }

  .header__mobile-nav-link {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 0;
    text-decoration: none;
    color: #333;
    font-size: 1.1rem;
    font-weight: 500;
  }

  .header__mobile-nav-arrow {
    font-size: 0.8rem;
    transition: transform 0.2s ease;
  }

  .header__mobile-dropdown[open] .header__mobile-nav-arrow {
    transform: rotate(180deg);
  }

  .header__mobile-dropdown-list {
    list-style: none;
    margin: 0;
    padding: 0 0 1rem 1rem;
  }

  .header__mobile-dropdown-item {
    margin: 0;
  }

  .header__mobile-dropdown-link {
    display: block;
    padding: 0.5rem 0;
    text-decoration: none;
    color: #666;
    font-size: 1rem;
  }

  .header__mobile-megamenu {
    padding: 0 0 1rem 1rem;
  }

  .header__mobile-megamenu-column {
    margin-bottom: 1.5rem;
  }

  .header__mobile-megamenu-heading {
    margin: 0 0 0.5rem 0;
    font-size: 1rem;
    font-weight: 600;
    color: #333;
  }

  .header__mobile-megamenu-links {
    list-style: none;
    margin: 0;
    padding: 0;
  }

  .header__mobile-megamenu-item {
    margin: 0;
  }

  .header__mobile-megamenu-link {
    display: block;
    padding: 0.25rem 0;
    text-decoration: none;
    color: #666;
    font-size: 0.9rem;
  }

  /* Force single row layout on desktop */
  @media screen and (min-width: 769px) {
    .header__inner {
      display: flex !important;
      flex-wrap: nowrap !important;
      align-items: center !important;
    }
    
    .header__nav {
      flex: 1 1 auto;
      min-width: 0;
    }
    
    .header__nav-list--desktop {
      display: flex !important;
      flex-wrap: nowrap !important;
    }
    
    .header__mobile-menu {
      display: none !important;
    }
    
    .header__mobile-menu-toggle {
      display: none !important;
    }
  }
  @media screen and (max-width: 1024px) and (min-width: 769px) {
    .header__nav {
      margin: 0 0.5rem;
    }
    .header__nav-list {
      gap: 1rem;
    }
    .header__nav-link {
      font-size: 0.875em;
      font-weight: 400;
      text-transform: uppercase;
    }
  }

  /* Hide desktop nav on mobile */
  @media screen and (max-width: 768px) {
    .header__inner {
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 1rem;
    }

    .header__nav-list--desktop {
      display: none;
    }

    .header__mobile-menu-toggle {
      display: flex;
      position: static;
      transform: none;
      z-index: auto;
      order: 1;
      flex-shrink: 0;
    }

    .header__nav {
      flex: 0;
      margin: 0;
      order: 1;
    }

    .header__logo {
      flex: 1;
      text-align: center;
      order: 2;
    }

    .header__icons {
      margin-left: 0;
      order: 3;
      flex-shrink: 0;
    }
  }

  /* Hide mobile elements on desktop */
  @media screen and (min-width: 769px) {
    .header__mobile-menu-toggle {
      display: none;
    }

    .header__mobile-menu {
      display: none;
    }
  }
</style>

<script>
  class MobileMenu {
    constructor() {
      this.init();
    }

    init() {
      this.mobileMenuToggle = document.querySelector('.header__mobile-menu-toggle');
      this.mobileMenu = document.querySelector('.header__mobile-menu');
      this.body = document.body;
      
      if (!this.mobileMenuToggle || !this.mobileMenu) return;
      
      this.bindEvents();
    }

    bindEvents() {
      // Toggle mobile menu
      this.mobileMenuToggle.addEventListener('click', (e) => {
        e.preventDefault();
        this.toggleMobileMenu();
      });

      // Close menu on backdrop click
      this.mobileMenu.addEventListener('click', (e) => {
        if (e.target === this.mobileMenu) {
          this.closeMobileMenu();
        }
      });

      // Close menu on escape key
      document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape' && this.mobileMenu.classList.contains('active')) {
          this.closeMobileMenu();
        }
      });

      // Close menu on window resize to desktop
      window.addEventListener('resize', () => {
        if (window.innerWidth > 768 && this.mobileMenu.classList.contains('active')) {
          this.closeMobileMenu();
        }
      });
    }

    toggleMobileMenu() {
      if (this.mobileMenu.classList.contains('active')) {
        this.closeMobileMenu();
      } else {
        this.openMobileMenu();
      }
    }

    openMobileMenu() {
      this.mobileMenu.classList.add('active');
      this.mobileMenuToggle.classList.add('active');
      this.mobileMenuToggle.setAttribute('aria-expanded', 'true');
      this.mobileMenu.setAttribute('aria-hidden', 'false');
      this.body.style.overflow = 'hidden';
      
      // Focus first focusable element
      const firstFocusable = this.mobileMenu.querySelector('a, button, summary');
      if (firstFocusable) {
        firstFocusable.focus();
      }
    }

    closeMobileMenu() {
      this.mobileMenu.classList.remove('active');
      this.mobileMenuToggle.classList.remove('active');
      this.mobileMenuToggle.setAttribute('aria-expanded', 'false');
      this.mobileMenu.setAttribute('aria-hidden', 'true');
      this.body.style.overflow = '';
      
      // Return focus to toggle button
      this.mobileMenuToggle.focus();
    }
  }

  // Initialize mobile menu when DOM is ready
  function updateHeaderHeightVar(){
    const sh = document.querySelector('.site-header');
    if (!sh) return;
    const h = Math.ceil(sh.getBoundingClientRect().height);
    document.documentElement.style.setProperty('--header-h', h + 'px');
  }
  function updateTopHeroFlag(){
    try {
      const main = document.getElementById('MainContent');
      let firstSection = null;
      try {
        firstSection = main?.querySelector(':scope > .shopify-section:first-child');
      } catch (_e) {
        firstSection = main?.querySelector('.shopify-section');
      }

      // Only treat our dedicated hero sections as valid top hero
      const hasHeroBanner = firstSection?.querySelector('.hero-banner');
      const hasHeroSplit = firstSection?.querySelector('.hero-split');
      const hasTopHero = !!(hasHeroBanner || hasHeroSplit);

      document.body.classList.toggle('has-top-hero', hasTopHero);

      // Apply hero overlay mode only when explicitly enabled AND the first section is a hero
      const overlayOnHero = {{ section.settings.overlay_on_hero | json }};

      if (overlayOnHero && hasTopHero) {
        document.body.classList.add('header--hero-overlay');
      } else {
        document.body.classList.remove('header--hero-overlay');
      }
    } catch(e) {
      console.error('Error in updateTopHeroFlag:', e);
    }
  }

  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      new MobileMenu();
      updateHeaderHeightVar();
      updateTopHeroFlag();
      window.addEventListener('resize', () => { updateHeaderHeightVar(); updateTopHeroFlag(); });
      // In case Shopify sections lazy-load, observe main for first-section changes
      const obs = new MutationObserver(() => updateTopHeroFlag());
      const main = document.getElementById('MainContent');
      if (main) obs.observe(main, { childList: true, subtree: false });
    });
  } else {
    new MobileMenu();
    updateHeaderHeightVar();
    updateTopHeroFlag();
    window.addEventListener('resize', () => { updateHeaderHeightVar(); updateTopHeroFlag(); });
  }
</script>

{% javascript %}
  (() => {
    const header = document.querySelector('[data-section-type="header"]');
    if (!header) return;

    const navWrappers = header.querySelectorAll('.header__nav-link-wrapper');
    let activeWrapper = null;
    let hoverTimeout = null;

    const showDropdown = (wrapper) => {
      const link = wrapper.querySelector('.header__nav-link');
      if (link) {
        link.setAttribute('aria-expanded', 'true');
        wrapper.classList.add('active');
        activeWrapper = wrapper;
      }
    };

    const hideDropdown = (wrapper) => {
      const link = wrapper.querySelector('.header__nav-link');
      if (link) {
        link.setAttribute('aria-expanded', 'false');
        wrapper.classList.remove('active');
        if (activeWrapper === wrapper) {
          activeWrapper = null;
        }
      }
    };

    const hideAllDropdowns = () => {
      navWrappers.forEach(wrapper => hideDropdown(wrapper));
    };

    navWrappers.forEach(wrapper => {
      const link = wrapper.querySelector('.header__nav-link');
      if (!link) return;

      // Mouse events
      wrapper.addEventListener('mouseenter', () => {
        if (hoverTimeout) {
          clearTimeout(hoverTimeout);
          hoverTimeout = null;
        }
        hideAllDropdowns();
        showDropdown(wrapper);
      });

      wrapper.addEventListener('mouseleave', () => {
        hoverTimeout = setTimeout(() => {
          hideDropdown(wrapper);
        }, 150);
      });

      // Keyboard events
      link.addEventListener('keydown', (e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          if (activeWrapper === wrapper) {
            hideDropdown(wrapper);
          } else {
            hideAllDropdowns();
            showDropdown(wrapper);
          }
        } else if (e.key === 'Escape') {
          hideAllDropdowns();
          link.focus();
        }
      });

      // Handle dropdown link navigation
      const dropdown = wrapper.querySelector('.header__dropdown, .header__megamenu');
      if (dropdown) {
        const dropdownLinks = dropdown.querySelectorAll('a');
        dropdownLinks.forEach((dropdownLink, index) => {
          dropdownLink.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
              hideDropdown(wrapper);
              link.focus();
            } else if (e.key === 'Tab' && e.shiftKey && index === 0) {
              // First link + Shift+Tab = go back to main link
              e.preventDefault();
              link.focus();
            } else if (e.key === 'Tab' && !e.shiftKey && index === dropdownLinks.length - 1) {
              // Last link + Tab = close dropdown and continue
              hideDropdown(wrapper);
            }
          });
        });
      }
    });

    // Close dropdowns when clicking outside
    document.addEventListener('click', (e) => {
      if (!header.contains(e.target)) {
        hideAllDropdowns();
      }
    });

    // Close dropdowns on page scroll
    window.addEventListener('scroll', () => {
      if (activeWrapper) {
        hideAllDropdowns();
      }
    });
  })();
{% endjavascript %}

<script>
// Sticky Header Implementation
(() => {
  const header = document.getElementById('SiteHeader');
  if (!header) return;

  const mode = header.dataset.stickyMode;              // none | always | on-scroll-up
  const breakpoint = header.dataset.stickyBreakpoint;  // all | desktop | mobile
  const threshold = parseInt(header.dataset.revealThreshold || '24', 10);
  const animMs = parseInt(header.dataset.animationMs || '150', 10);
  const shadowOn = header.dataset.shadowWhenStuck === 'true';
  const shadowStrength = header.dataset.shadowStrength || 'light';
  const solidBackground = header.dataset.solidBackground === 'true';
  const paddingReduction = parseInt(header.dataset.paddingReduction || '8', 10);
  const respectReducedMotion = header.dataset.respectReducedMotion === 'true';

  // Apply animation duration via CSS var
  header.style.setProperty('--header-anim', `${animMs}ms`);
  header.style.setProperty('--padding-reduction', `${paddingReduction}px`);
  
  // Ensure header z-index is properly set
  const zIndex = header.style.getPropertyValue('--header-z') || '100';
  header.style.setProperty('--header-z', zIndex);

  // Respect reduced motion
  if (respectReducedMotion && window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
    header.style.setProperty('--header-anim', '0ms');
  }

  // Breakpoint gating
  const mqDesktop = window.matchMedia('(min-width: 990px)');
  const inScope = () => {
    if (breakpoint === 'all') return true;
    if (breakpoint === 'desktop') return mqDesktop.matches;
    if (breakpoint === 'mobile') return !mqDesktop.matches;
    return true;
  };

  // Measure header height and set --header-h (used for scroll-padding and main padding when fixed)
  const applyHeaderHeight = () => {
  const h = Math.ceil(header.getBoundingClientRect().height);
  // Expose both variables for compatibility: --header-h is used for scroll-padding,
  // --header-height is used by sections (e.g. hero fullscreen offset).
  document.documentElement.style.setProperty('--header-h', `${h}px`);
  document.documentElement.style.setProperty('--header-height', `${h}px`);
  };
  
  const ro = new ResizeObserver(applyHeaderHeight);
  ro.observe(header);
  applyHeaderHeight();

  // Handle "none" mode - natural flow with no extra spacing
  if (mode === 'none') {
    // Add class to override CSS padding
    document.body.classList.add('header--natural-flow');

    const mainContent = document.getElementById('MainContent');
    const announcementBar = document.querySelector('[data-section-type="announcement"]');

    const applyNormalLayout = () => {
      const headerHeight = Math.ceil(header.getBoundingClientRect().height);
      const announcementHeight = announcementBar ? Math.ceil(announcementBar.getBoundingClientRect().height) : 0;
      const totalHeaderOffset = headerHeight + announcementHeight;
      const isHeroOverlay = document.body.classList.contains('header--hero-overlay');
      const isBlackOnPages = document.body.classList.contains('header--black-on-pages');

      // Set CSS variables for other components to use
      document.documentElement.style.setProperty('--header-h', `${headerHeight}px`);
      document.documentElement.style.setProperty('--header-height', `${headerHeight}px`);
      document.documentElement.style.setProperty('--announcement-height', `${announcementHeight}px`);
      document.documentElement.style.setProperty('--total-header-offset', `${totalHeaderOffset}px`);

      if (mainContent) {
        if (isHeroOverlay) {
          mainContent.style.paddingTop = '0';
          mainContent.style.marginTop = '0';
        } else if (isBlackOnPages) {
          // Black on pages mode overrides natural flow
          mainContent.style.paddingTop = `${totalHeaderOffset}px`;
          mainContent.style.marginTop = '0';
        } else {
          mainContent.style.paddingTop = '0';
          mainContent.style.marginTop = '0';
        }
      }
    };

    applyNormalLayout();
    window.addEventListener('resize', applyNormalLayout);
    return;
  } else {
    // Remove natural flow class if not in "none" mode
    document.body.classList.remove('header--natural-flow');
  }

  // "always" relies on CSS sticky only
  if (mode === 'always') {
    const mainContent = document.getElementById('MainContent');
    const announcementBar = document.querySelector('[data-section-type="announcement"]');

    const applyFixedLayout = () => {
      const headerHeight = Math.ceil(header.getBoundingClientRect().height);
      const announcementHeight = announcementBar ? Math.ceil(announcementBar.getBoundingClientRect().height) : 0;
      const totalHeaderOffset = headerHeight + announcementHeight;
      const isHeroOverlay = document.body.classList.contains('header--hero-overlay');

      document.documentElement.style.setProperty('--header-h', `${headerHeight}px`);
      document.documentElement.style.setProperty('--header-height', `${headerHeight}px`);
      document.documentElement.style.setProperty('--announcement-height', `${announcementHeight}px`);
      document.documentElement.style.setProperty('--total-header-offset', `${totalHeaderOffset}px`);

      if (mainContent) {
        if (isHeroOverlay) {
          mainContent.style.paddingTop = '0px';
          mainContent.style.marginTop = '0';
        } else {
          mainContent.style.paddingTop = `${totalHeaderOffset}px`;
          mainContent.style.marginTop = '0';
        }
      }

      header.style.top = announcementHeight > 0 ? `${announcementHeight}px` : '0';
    };

    header.classList.add('is-sticky-always');
    header.style.position = 'fixed';
    header.style.setProperty('position', 'fixed');
    header.style.left = '0';
    header.style.right = '0';
    header.style.width = '100%';
    header.style.zIndex = zIndex;

    applyFixedLayout();
    window.addEventListener('resize', applyFixedLayout);
    mqDesktop.addEventListener?.('change', applyFixedLayout);
    return;
  }

  // No other modes supported - header stays in natural flow
})();

// Gucci fade: background overlay fade-in on scroll, with optional float-over on top hero
(() => {
  const header = document.getElementById('SiteHeader');
  if (!header) return;

  const gucciOn = header.dataset.gucciStyle === 'true';
  if (!gucciOn) return;

  const overlayOnHero = header.dataset.overlayOnHero === 'true';
  const stickyMode = header.dataset.stickyMode; // none | always
  const glassEffect = header.dataset.glassEffect; // none | subtle | full
  const genericRange = parseInt(header.dataset.gucciRange || '180', 10);

  document.body.classList.add('gucci-enabled');

  const main = document.getElementById('MainContent');

  const getTopHero = () => {
    try {
      const first = main?.querySelector(':scope > .shopify-section:first-child');
      return first?.querySelector('.hero-banner, .hero-split') || null;
    } catch (_e) {
      const first = main?.querySelector('.shopify-section');
      return first?.querySelector('.hero-banner, .hero-split') || null;
    }
  };

  let topHeroEl = getTopHero();
  let hasTopHero = !!topHeroEl;
  let heroEl = overlayOnHero ? topHeroEl : null; // used only for overlay positioning
  let hasHeroOverlay = overlayOnHero && hasTopHero;

  // Drive overlay mode strictly by setting + top-hero presence
  document.body.classList.toggle('header--hero-overlay', hasHeroOverlay);

  // Initialize background/blur vars
  header.style.setProperty('--header-bg-alpha', '0');
  header.style.setProperty('--header-blur', 'none');

  // Initial text theme: start white whenever a top hero exists, regardless of float-over
  if (hasTopHero) {
    header.classList.add('theme--light-on-hero');
    header.classList.remove('theme--dark-over-content');
  } else {
    header.classList.add('theme--dark-over-content');
    header.classList.remove('theme--light-on-hero');
  }

  const clamp01 = v => Math.max(0, Math.min(1, v));
  const announcement = document.querySelector('[data-section-type="announcement"]');
  const getAnnounceH = () => Math.max(0, (announcement?.getBoundingClientRect().height || 0));

  // Keep context fresh when sections change in the editor
  const recomputeContext = () => {
    topHeroEl = getTopHero();
    hasTopHero = !!topHeroEl;
    heroEl = overlayOnHero ? topHeroEl : null;
    hasHeroOverlay = overlayOnHero && hasTopHero;
    document.body.classList.toggle('header--hero-overlay', hasHeroOverlay);

    if (hasTopHero) {
      header.classList.add('theme--light-on-hero');
      header.classList.remove('theme--dark-over-content');
    } else {
      header.classList.add('theme--dark-over-content');
      header.classList.remove('theme--light-on-hero');
    }
  };

  let heroRange = 200;
  const computeHeroRange = () => {
    const h = (hasTopHero ? (topHeroEl?.getBoundingClientRect().height || 0) : (heroEl?.getBoundingClientRect().height || 0));
    return Math.min(320, Math.max(80, h * 0.6));
  };
  if (hasTopHero) heroRange = computeHeroRange();
  window.addEventListener('resize', () => { if (hasTopHero) heroRange = computeHeroRange(); });

  // Observe first-section changes (theme editor)
  const mainObserver = new MutationObserver(recomputeContext);
  if (main) mainObserver.observe(main, { childList: true });

  let lastAlpha = -1;
  const FLIP_THRESHOLD = 0.22;
  const update = () => {
    let alpha = 0;

    if (hasTopHero) {
      // Use hero bottom for fade progression regardless of float-over
      const r = (topHeroEl || heroEl)?.getBoundingClientRect();
      const aH = getAnnounceH();
      const bottomToTop = aH - (r?.bottom || 0);
      alpha = clamp01((bottomToTop + heroRange) / heroRange);

      if (hasHeroOverlay && heroEl && stickyMode !== 'always') {
        // Only juggle position in true overlay (float-over) mode
        header.style.top = `${aH}px`;
        header.style.left = '0';
        header.style.right = '0';
        header.style.position = alpha >= 1 ? 'fixed' : 'absolute';
        header.classList.toggle('is-fixed-over-content', alpha >= 1);
      }
    } else {
      // No top hero: fade background by scroll distance, never flip text color
      const y = window.scrollY || window.pageYOffset || 0;
      alpha = clamp01(y / genericRange);
      header.classList.remove('is-fixed-over-content');
    }

    if (Math.abs(alpha - lastAlpha) > 0.01) {
      lastAlpha = alpha;

      if (glassEffect === 'none') {
        header.style.setProperty('--header-bg-alpha', alpha.toFixed(3));
        header.style.setProperty('--header-blur', 'none');
      } else if (glassEffect === 'subtle') {
        header.style.setProperty('--header-bg-alpha', Math.min(alpha, 0.08).toFixed(3));
        header.style.setProperty('--header-blur', alpha > 0 ? 'blur(8px) saturate(180%)' : 'none');
      } else {
        // full
        const glassAlpha = Math.min(alpha * 0.85, 0.85);
        header.style.setProperty('--header-bg-alpha', glassAlpha.toFixed(3));
        header.style.setProperty('--header-blur', alpha > 0 ? 'blur(8px) saturate(180%)' : 'none');
      }

      if (hasTopHero) {
        if (alpha >= FLIP_THRESHOLD) {
          header.classList.add('theme--dark-over-content');
          header.classList.remove('theme--light-on-hero');
        } else {
          header.classList.add('theme--light-on-hero');
          header.classList.remove('theme--dark-over-content');
        }
      } else {
        header.classList.add('theme--dark-over-content');
        header.classList.remove('theme--light-on-hero');
      }
    }

    requestAnimationFrame(update);
  };

  requestAnimationFrame(update);
})();
</script>

{% schema %}
{
  "name": "🏠 Header",
  "tag": "header",
  "class": "header-section",
  "settings": [
    {
      "type": "paragraph",
      "content": "Create your navigation by adding Simple Link or Megamenu blocks below. Each block represents a top-level navigation item."
    },
    {
      "type": "header",
      "content": "🖼️ Logo Settings"
    },
    {
      "type": "image_picker",
      "id": "logo",
      "label": "Logo image",
      "info": "Your store logo (recommended: PNG or SVG format)"
    },
    {
      "type": "image_picker",
      "id": "logo_mobile",
      "label": "Mobile logo (optional)",
      "info": "Different logo for mobile devices (leave empty to use main logo)",
      "visible_if": "{{ section.settings.logo != blank }}"
    },
    {
      "type": "image_picker",
      "id": "logo_light",
      "label": "Logo image (light/overlay)",
      "info": "Shown when the header starts over a hero with Gucci-style. Upload a white/transparent version."
    },
    {
      "type": "image_picker",
      "id": "logo_mobile_light",
      "label": "Mobile logo (light/overlay)",
      "info": "Optional mobile-specific light logo for overlay mode"
    },
    {
      "type": "range",
      "id": "logo_width",
      "min": 50,
      "max": 400,
      "step": 5,
      "unit": "px",
      "label": "Logo width",
      "default": 150,
      "info": "Maximum width of your logo"
    },
    {
      "type": "range",
      "id": "logo_font_size",
      "min": 16,
      "max": 48,
      "step": 2,
      "unit": "px",
      "label": "Logo text size",
      "default": 20,
      "info": "Font size for text logo (when no image is uploaded)"
    },
    {
      "type": "header",
      "content": "📌 Sticky Header Behavior"
    },
    {
      "type": "select",
      "id": "sticky_mode",
      "label": "Sticky header behavior",
      "options": [
        { "value": "none", "label": "None" },
        { "value": "always", "label": "Always sticky" }
      ],
      "default": "none",
      "info": "None = header scrolls away normally. Always = header stays visible while scrolling."
    },
    {
      "type": "select",
      "id": "sticky_breakpoint",
      "label": "Applies to",
      "options": [
        { "value": "all", "label": "All devices" },
        { "value": "desktop", "label": "Desktop only" },
        { "value": "mobile", "label": "Mobile only" }
      ],
      "default": "desktop",
      "info": "Desktop = screens ≥ 990px width, Mobile = screens < 990px width"
    },

    {
      "type": "header",
      "content": "🎨 Visual Effects",
      "visible_if": "{{ section.settings.sticky_mode != 'none' }}"
    },
    {
      "type": "checkbox",
      "id": "overlay_on_hero",
      "label": "Float over hero sections",
      "default": false,
      "info": "When enabled, header will float transparently over hero banners and hero split sections with no gap."
    },
    {
      "type": "checkbox",
      "id": "gucci_style_behavior",
      "label": "Gucci-style scroll behavior",
      "default": false,
      "info": "Fades in a white background as you scroll. If the first section is a Hero and 'Float over hero sections' is enabled, the header starts light (white text) over the hero and transitions to dark text as the background appears. On other pages it starts dark and stays dark."
    },
    {
      "type": "select",
      "id": "glass_effect",
      "label": "Glass/blur effect style",
      "default": "full",
      "options": [
        {
          "value": "none",
          "label": "None - Solid background"
        },
        {
          "value": "subtle",
          "label": "Subtle - Minimal glass effect"
        },
        {
          "value": "full",
          "label": "Full - Strong glass effect"
        }
      ],
      "info": "Choose the glass effect style: None (solid background), Subtle (minimal glass), or Full (strong frosted glass)."
    },
    {
      "type": "checkbox",
      "id": "shadow_when_stuck",
      "label": "Add shadow when sticky",
      "default": true,
      "info": "Adds a subtle drop shadow below the header when it becomes sticky for better visual separation.",
      "visible_if": "{{ section.settings.sticky_mode != 'none' }}"
    },
    {
      "type": "select",
      "id": "shadow_strength",
      "label": "Shadow strength",
      "options": [
        { "value": "none", "label": "None" },
        { "value": "light", "label": "Light" },
        { "value": "medium", "label": "Medium" }
      ],
      "default": "light",
      "info": "Controls shadow darkness. Light = subtle, Medium = more pronounced. Only visible when shadow is enabled.",
      "visible_if": "{{ section.settings.sticky_mode != 'none' and section.settings.shadow_when_stuck == true }}"
    },
    {
      "type": "checkbox",
      "id": "solid_background_when_stuck",
      "label": "Solid background when sticky",
      "default": true,
      "info": "Ensures header has an opaque background when sticky to prevent content from showing through.",
      "visible_if": "{{ section.settings.sticky_mode != 'none' }}"
    },
    {
      "type": "header",
      "content": "📐 Layout & Spacing",
      "visible_if": "{{ section.settings.sticky_mode != 'none' }}"
    },
    {
      "type": "range",
      "id": "padding_reduction",
      "label": "Padding reduction when sticky",
      "min": 0,
      "max": 20,
      "step": 2,
      "unit": "px",
      "default": 8,
      "info": "Reduces header top/bottom padding when sticky to save vertical space. 0px = no change, 20px = maximum reduction.",
      "visible_if": "{{ section.settings.sticky_mode != 'none' }}"
    },
    {
      "type": "range",
      "id": "z_index",
      "label": "Header z-index",
      "min": 50,
      "max": 500,
      "step": 10,
      "default": 100,
      "info": "Controls stacking order. Higher values appear above other elements. Adjust if header appears behind popups or modals.",
      "visible_if": "{{ section.settings.sticky_mode != 'none' }}"
    },
    {
      "type": "header",
      "content": "🧭 Navigation (Legacy Fallback)"
    },
    {
      "type": "paragraph",
      "content": "⚠️ Legacy settings below are used only when no navigation blocks are added above. For modern navigation, use the blocks system instead."
    },
    {
      "type": "link_list",
      "id": "menu",
      "label": "Menu",
      "default": "main-menu",
      "info": "Select a menu for navigation (fallback if no blocks are configured)"
    }
  ],
  "blocks": [
    {
      "type": "simple_link",
      "name": "🔗 Simple Link",
      "settings": [
        {
          "type": "paragraph",
          "content": "Create a simple dropdown menu by selecting a menu from your navigation settings."
        },
        {
          "type": "link_list",
          "id": "menu",
          "label": "📋 Menu to display",
          "default": "main-menu",
          "info": "Choose which menu appears in this navigation item. The menu title becomes the link text."
        }
      ]
    },
    {
      "type": "megamenu",
      "name": "🎛️ Megamenu",
      "settings": [
        {
          "type": "paragraph",
          "content": "Create a large, multi-column menu with promotional content. Perfect for showcasing categories and featured products."
        },
        {
          "type": "text",
          "id": "title",
          "label": "🏷️ Link title",
          "default": "Shop",
          "placeholder": "Shop, Catalog, Products...",
          "info": "Text that appears in the main navigation"
        },
        {
          "type": "header",
          "content": "📋 Menu Columns"
        },
        {
          "type": "link_list",
          "id": "menu_1",
          "label": "Column 1 menu",
          "info": "First column of links in the megamenu"
        },
        {
          "type": "link_list",
          "id": "menu_2",
          "label": "Column 2 menu",
          "info": "Second column of links in the megamenu"
        },
        {
          "type": "link_list",
          "id": "menu_3",
          "label": "Column 3 menu",
          "info": "Third column of links in the megamenu"
        },
        {
          "type": "header",
          "content": "🎯 Promotion (Optional)"
        },
        {
          "type": "image_picker",
          "id": "promo_image",
          "label": "Promotional image",
          "info": "Featured image displayed in the megamenu sidebar"
        },
        {
          "type": "text",
          "id": "promo_heading",
          "label": "Promotional headline",
          "placeholder": "New Collection, Sale, Featured...",
          "info": "Text overlay or caption for the promotional content",
          "visible_if": "{{ block.settings.promo_image != blank }}"
        },
        {
          "type": "url",
          "id": "promo_link",
          "label": "Promotional link",
          "info": "Where should the promotional content link to?",
          "visible_if": "{{ block.settings.promo_image != blank }}"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Header",
      "settings": {
        "logo_width": 150
      },
      "blocks": [
        {
          "type": "simple_link",
          "settings": {
            "menu": "main-menu"
          }
        }
      ]
    }
  ],
  "max_blocks": 8,
  "enabled_on": {
    "groups": ["header"]
  }
}
{% endschema %}
