{%- comment -%}
  Enhanced Collection Section with Color Swatch Cards
  
  Purpose: Product grid with optional color swatches and hover preview
  - Card hover preview swaps main image to variant media
  - Click behavior: update product links or trigger quick add
  - Configurable swatch display and interaction settings
  - Responsive grid with accessibility support
  
  Features:
  - Color swatch hover preview on cards
  - Quick add functionality
  - Responsive product grid
  - Filter and sort integration ready
  - Performance optimized with lazy loading
{%- endcomment -%}

{% comment %}theme-check-disable ParserBlockingJavaScript{% endcomment %}
<script src="{{ 'variant-hover-clean.js' | asset_url }}" defer></script>
{%- render 'color-swatch-mapping' -%}

<style>
  .collection {
    padding: 2rem 0;
    --collection-title-font-weight: normal;
    --collection-title-font-size: 2rem;
    --collection-description-font-weight: normal;
    --collection-description-font-size: 1.3rem;
  }
  
  .collection__container {
    /* Remove container constraints to allow full width */
  }
  
  .collection__header {
    margin-bottom: 3rem;
    text-align: center;
  }
  
  .collection__title {
    font-weight: var(--collection-title-font-weight);
    margin: 0 0 1rem;
  }
  
  /* Title font sizes - responsive with clamp() */
  .collection__title.size-small {
    font-size: clamp(1.25rem, 2.5vw, 1.75rem);
  }
  .collection__title.size-medium {
    font-size: clamp(1.8rem, 3vw, 2.8rem);
  }
  .collection__title.size-large {
    font-size: clamp(2.6rem, 4vw, 4.2rem);
  }
  .collection__title.size-extra-large {
    font-size: clamp(3.2rem, 5vw, 5.6rem);
  }
  
  .collection__description {
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
    font-weight: var(--collection-description-font-weight);
  }
  
  /* Description font sizes - responsive with clamp() */
  .collection__description.size-small {
    font-size: clamp(1rem, 1.5vw, 1.125rem);
  }
  .collection__description.size-medium {
    font-size: clamp(1.2rem, 2vw, 1.4rem);
  }
  .collection__description.size-large {
    font-size: clamp(1.4rem, 2.2vw, 1.6rem);
  }
  
  .collection-grid {
    list-style: none; padding: 0; margin: 0;
    display: grid;
    grid-template-columns: repeat(var(--cols-mobile), 1fr);
    gap: 0 2px; /* row-gap column-gap */
    margin-bottom: 3rem;
  }

  /* Enhanced grid for media highlights */
  .collection-grid[data-grid-enhanced] {
    grid-template-columns: repeat(var(--cols-mobile), 1fr);
    grid-auto-rows: 1fr; /* Make all grid items same height */
    gap: 0 2px;
  }
  
  /* Tablet breakpoint */
  @media (min-width: 750px) {
    .collection-grid {
      grid-template-columns: repeat(var(--cols-tablet), 1fr);
    }
  }
  
  /* Desktop breakpoint */
  @media (min-width: 990px) {
    .collection-grid {
      grid-template-columns: repeat(4, minmax(0, 1fr)); /* force 4-up */
    }
    
    /* Media cards span 2 columns on desktop */
    .collection-grid .media-card {
      grid-column: span 2;
    }
  }
  
  /* Card swatch styles */
  .card__swatches {
    display: flex;
    gap: 0.25rem;
    margin: 0.75rem 0;
    justify-content: center;
    flex-wrap: wrap;
  }
  
  .swatch-wrapper {
    position: relative;
  }
  
  .swatch-input {
    position: absolute;
    opacity: 0;
    pointer-events: none;
  }
  
  .swatch {
    display: block;
    width: 20px;
    height: 20px;
    min-width: 20px; /* Prevent compression */
    min-height: 20px; /* Prevent compression */
    max-width: 20px; /* Prevent expansion */
    max-height: 20px; /* Prevent expansion */
    border-radius: 50%;
    border: 1px solid rgba(0,0,0,0.1);
    cursor: pointer;
    transition: all 0.2s ease;
    flex-shrink: 0; /* Prevent flex compression */
    aspect-ratio: 1 / 1; /* Ensure perfect square aspect ratio */
    box-sizing: border-box; /* Include border in size calculation */
    
    /* Button-specific overrides */
    padding: 0; /* Remove default button padding */
    margin: 0; /* Remove default button margin */
    font-size: 0; /* Remove font size influence */
    line-height: 1; /* Reset line height */
    text-align: center; /* Center any content */
    vertical-align: top; /* Prevent baseline alignment issues */
    outline: none; /* Remove default focus outline */
  }
  
  .swatch:hover,
 .swatch:focus {
    transform: scale(1.2);
    border-color: currentColor;
  }
  
  .swatch-input:checked + .swatch {
    border-color: currentColor;
    box-shadow: 0 0 0 1px white, 0 0 0 3px currentColor;
  }
  
  .swatch-more {
    display: flex;
    align-items: center;
    font-size: 0.75rem;
    color: rgba(var(--color-foreground), 0.7);
  }
  
  /* Mobile responsive adjustments */
  @media (max-width: 749px) {
    /* Media cards take full width on mobile */
    .collection-grid[data-grid-enhanced] .media-card {
      grid-column: span var(--cols-mobile);
    }
    
    .collection__header {
      padding-left: 1rem;
      padding-right: 1rem;
    }
    
    .collection__title {
      font-size: 2rem;
    }
  }
  
  @media (max-width: 479px) {
    .collection__header {
      padding-left: 0.75rem;
      padding-right: 0.75rem;
    }
  }

  /* Make this section full-bleed even if a parent has max-width */
  section[data-section="{{ section.id }}"] .collection__container{
    width: 100vw;
    max-width: 100vw;
    margin-left: 50%;
    transform: translateX(-50%);
    padding-left: max(12px, 2vw);
    padding-right: max(12px, 2vw);
  }

  /* Guarantee 4 columns at desktop */
  @media (min-width: 990px){
    section[data-section="{{ section.id }}"] .collection-grid{
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }

  /* Let grid items shrink so they don't force wrapping */
  section[data-section="{{ section.id }}"] .collection-grid > *{ min-width: 0; }

  /* Remove theme card width caps that often restrict columns */
  section[data-section="{{ section.id }}"] .card,
  section[data-section="{{ section.id }}"] .card-wrapper,
  section[data-section="{{ section.id }}"] .grid__item{
    max-width: none;
    width: 100%;
  }

  /* neutralize Dawn grid widths on items so CSS Grid controls layout */
  section[data-section="{{ section.id }}"] .collection-grid > .grid__item{
    margin: 0 !important;
    padding: 0 !important;
    width: auto !important;
    max-width: none !important;
    grid-column: auto / span 1 !important;
    min-width: 0;
  }

  /* Hide empty promo blocks */
  .product-grid__promo-block:empty { display: none; }

  /* Edge-to-edge grid */
  .collection-product-grid__inner.page-width {
    max-width: none;
    padding-left: 0;
    padding-right: 0;
  }
  
  /* Card Hover Overlay Styles for Collection Section */
  .card-hover-overlay {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    height: auto;
    background: rgba(255, 255, 255, 0.95);
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: opacity 0.25s ease;
    pointer-events: none;
    padding: 1rem;
  }
  
  .card-hover-overlay__content {
    background: transparent;
    border-radius: 0;
    padding: 0.5rem 0;
    text-align: center;
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
  }
  
  /* Color swatches in overlay */
  .overlay-colors {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    order: 1;
    margin-bottom: 0.25rem;
  }
  
  .overlay-colors .card__swatches {
    display: flex !important;
    gap: 0.5rem;
    margin: 0;
    justify-content: center;
    visibility: visible !important;
  }
  
  /* Target swatches inside overlay - more specific selectors */
  .card-hover-overlay .overlay-colors .swatch,
  .card-hover-overlay .swatch {
    width: 1.5rem !important;
    height: 1.5rem !important;
    border-radius: 50% !important;
    border: 2px solid rgba(255, 255, 255, 0.8) !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2) !important;
    display: block !important;
    position: relative !important;
    opacity: 1 !important;
    visibility: visible !important;
  }
  
  /* Color mappings for overlay swatches */
  .card-hover-overlay .swatch--pink { background-color: #f8bbd9 !important; }
  .card-hover-overlay .swatch--black { background-color: #000000 !important; }
  .card-hover-overlay .swatch--white { background-color: #ffffff !important; border-color: #e0e0e0 !important; }
  .card-hover-overlay .swatch--red { background-color: #dc2626 !important; }
  .card-hover-overlay .swatch--blue { background-color: #2563eb !important; }
  .card-hover-overlay .swatch--green { background-color: #4a5d3a !important; }
  .card-hover-overlay .swatch--yellow { background-color: #facc15 !important; }
  .card-hover-overlay .swatch--purple { background-color: #9333ea !important; }
  .card-hover-overlay .swatch--orange { background-color: #ea580c !important; }
  .card-hover-overlay .swatch--brown { background-color: #92400e !important; }
  .card-hover-overlay .swatch--gray,
  .card-hover-overlay .swatch--grey { background-color: #6b7280 !important; }
  .card-hover-overlay .swatch--navy { background-color: #1e3a8a !important; }
  .card-hover-overlay .swatch--beige { background-color: #f5f5dc !important; }
  .card-hover-overlay .swatch--cream { background-color: #fffdd0 !important; }
  
  /* Striped pattern for overlay swatches */
  .card-hover-overlay .swatch--striped {
    background: linear-gradient(45deg, #f0f0f0 25%, transparent 25%),
                linear-gradient(-45deg, #f0f0f0 25%, transparent 25%),
                linear-gradient(45deg, transparent 75%, #f0f0f0 75%),
                linear-gradient(-45deg, transparent 75%, #f0f0f0 75%) !important;
    background-size: 4px 4px !important;
    background-position: 0 0, 0 2px, 2px -2px, -2px 0px !important;
    border: 2px solid #ccc !important;
  }
  
  /* Fallback for unmapped colors */
  .card-hover-overlay .swatch:not([class*="swatch--"]) {
    background: linear-gradient(45deg, #f0f0f0 25%, transparent 25%),
                linear-gradient(-45deg, #f0f0f0 25%, transparent 25%),
                linear-gradient(45deg, transparent 75%, #f0f0f0 75%),
                linear-gradient(-45deg, transparent 75%, #f0f0f0 75%) !important;
    background-size: 4px 4px !important;
    background-position: 0 0, 0 2px, 2px -2px, -2px 0px !important;
    border: 2px solid #ccc !important;
  }
  
  .card-hover-overlay .overlay-colors .swatch:hover,
  .card-hover-overlay .swatch:hover {
    transform: scale(1.15) !important;
    border-color: white !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3) !important;
  }
  
  /* Size options in overlay */
  .overlay-sizes {
    display: flex;
    justify-content: center;
    gap: 0.375rem;
    flex-wrap: wrap;
    order: 2;
    margin-bottom: 0.25rem;
  }
  
  .size-btn {
    background: white;
    border: 1px solid rgba(0, 0, 0, 0.2);
    padding: 0.4rem 0.6rem;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 400;
    font-size: 0.8rem;
    transition: all 0.2s ease;
    min-width: 2rem;
    color: #333;
  }
  
  .size-btn:hover {
    background: #f5f5f5;
    border-color: rgba(0, 0, 0, 0.3);
  }
  
  /* Quick add button in overlay */
  .overlay-quick-add {
    background: black;
    color: white;
    border: none;
    padding: 0.6rem 1.5rem;
    border-radius: 4px;
    font-weight: 600;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.2s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    order: 3;
  }
  
  .overlay-quick-add:hover {
    background: #333;
  }
  
  /* Show overlay on card hover */
  .collection .card-wrapper:hover .card-hover-overlay {
    opacity: 1;
    pointer-events: auto;
  }
  
  /* Hide original swatches when in hover overlay mode, but only after overlays are created */
  .collection[data-swatch-mode="hover_overlay"][data-overlays-ready] .card__content .card__swatches {
    display: none !important; /* hides the below-card swatches only */
  }
  
  /* (optional safety) make sure overlay swatches are explicitly shown */
  .collection .card-hover-overlay .card__swatches {
    display: flex !important;
  }
  
  /* Enable hover overlay for collection section */
  .collection[data-swatch-mode="hover_overlay"] .card-wrapper:hover .card-hover-overlay,
  .collection[data-swatch-mode="hover_overlay"] .card:hover .card-hover-overlay {
    opacity: 1;
    pointer-events: auto;
  }
</style>

{{ 'collection-grid-utilities.css' | asset_url | stylesheet_tag }}

<section class="collection section" data-section="{{ section.id }}" data-swatch-mode="{{ section.settings.swatch_display_style | default: 'below_card' }}">
  <div class="collection__container">
  {%- if collection.title != blank and section.settings.show_collection_title or collection.description != blank and section.settings.show_collection_description -%}
      <div class="collection__header">
        {%- if collection.title != blank and section.settings.show_collection_title -%}
          <h1 class="collection__title size-{{ section.settings.collection_title_font_size | default: 'large' }}">{{ collection.title | escape }}</h1>
        {%- endif -%}
        {%- if collection.description != blank and section.settings.show_collection_description -%}
          <div class="collection__description size-{{ section.settings.collection_description_font_size | default: 'medium' }}">
            {{ collection.description }}
          </div>
        {%- endif -%}
      </div>
    {%- endif -%}

    {%- if collection.products.size > 0 -%}
      <div
        class="collection-grid"
        data-grid-enhanced
        style="
          --cols-desktop: {{ section.settings.columns_desktop }};
          --cols-tablet: {{ section.settings.columns_tablet }};
          --cols-mobile: {{ section.settings.columns_mobile }};
          --collection-title-font-weight: {{ section.settings.collection_title_font_weight }};
          --collection-description-font-weight: {{ section.settings.collection_description_font_weight }};
        "
      >
        {% assign product_index = 0 %}
        {% for product in collection.products limit: section.settings.products_per_page %}
            {% assign product_index = product_index | plus: 1 %}

            {%- comment -%} Optional promo block injection {%- endcomment -%}
            {% for block in section.blocks %}
              {% if block.type == 'promo' and block.settings.insert_after == product_index %}
                {% liquid
                  assign has_promo = false
                  if block.settings.promo_type == 'image' and block.settings.image != blank
                    assign has_promo = true
                  elsif block.settings.promo_type == 'richtext' and block.settings.html != blank
                    assign has_promo = true
                  endif
                %}
                {% if has_promo %}
                  <div class="collection-product-grid__promo-block" {{ block.shopify_attributes }}>
                    {% if block.settings.promo_type == 'image' %}
                      <div class="collection-product-grid__promo-image">
                        {{ block.settings.image | image_url: width: 600 | image_tag: loading: 'lazy', class: 'collection-product-grid__promo-img', widths: '300, 400, 500, 600', alt: block.settings.image.alt }}
                      </div>
                    {% else %}
                      <div class="collection-product-grid__promo-content rte">
                        {{ block.settings.html }}
                      </div>
                    {% endif %}
                  </div>
                {% endif %}
              {% endif %}
            {% endfor %}

            {% render 'card-product',
              product: product,
              collection: collection,
              show_swatches: section.settings.show_swatches_on_cards,
              enable_hover_preview: section.settings.enable_card_hover_preview,
              max_swatches: section.settings.max_swatches_per_product,
              card_click_behavior: section.settings.card_click_behavior,
              show_quick_add: section.settings.enable_quick_add,
              card_image_aspect_ratio: section.settings.card_image_aspect_ratio,
              enable_favorites: section.settings.enable_favorites,
              show_new_badges: section.settings.show_new_badges
            %}
          {% endfor %}
        </div>
      {%- else -%}
        <div class="collection-empty">
          <h2>{{ 'collections.general.no_matches' | t | default: 'No products found' }}</h2>
          <p>{{ 'collections.general.no_products_html' | t | default: 'Try adjusting your filters or adding products to this collection.' }}</p>
        </div>
      {%- endif -%}
  </div>
</section>

{% schema %}
{
  "name": "Collection",
  "tag": "section",
  "class": "section",
  "settings": [
    {
      "type": "header",
      "content": "Collection display"
    },
    {
      "type": "checkbox",
      "id": "show_collection_title",
      "label": "Show collection title",
      "default": true
    },
    {
      "type": "select",
      "id": "collection_title_font_weight",
      "label": "Collection title font weight",
      "default": "normal",
      "options": [
        {
          "value": "normal",
          "label": "Normal"
        },
        {
          "value": "bold",
          "label": "Bold"
        }
      ]
    },
    {
      "type": "select",
      "id": "collection_title_font_size",
      "label": "Collection title font size",
      "default": "large",
      "options": [
        {
          "value": "small",
          "label": "Small"
        },
        {
          "value": "medium",
          "label": "Medium"
        },
        {
          "value": "large",
          "label": "Large"
        },
        {
          "value": "extra-large",
          "label": "Extra Large"
        }
      ]
    },
    {
      "type": "checkbox",
      "id": "show_collection_description",
      "label": "Show collection description",
      "default": true
    },
    {
      "type": "select",
      "id": "collection_description_font_weight",
      "label": "Collection description font weight",
      "default": "normal",
      "options": [
        {
          "value": "normal",
          "label": "Normal"
        },
        {
          "value": "bold",
          "label": "Bold"
        }
      ]
    },
    {
      "type": "select",
      "id": "collection_description_font_size",
      "label": "Collection description font size",
      "default": "medium",
      "options": [
        {
          "value": "small",
          "label": "Small"
        },
        {
          "value": "medium",
          "label": "Medium"
        },
        {
          "value": "large",
          "label": "Large"
        }
      ]
    },
    {
      "type": "range",
      "id": "products_per_page",
      "min": 4,
      "max": 48,
      "step": 4,
      "default": 20,
      "label": "Products per page"
    },
    {
      "type": "header",
      "content": "Grid layout"
    },
    {
      "type": "range",
      "id": "columns_desktop",
      "min": 2,
      "max": 6,
      "step": 1,
      "default": 4,
      "label": "Columns on desktop"
    },
    {
      "type": "range",
      "id": "columns_tablet",
      "min": 2,
      "max": 4,
      "step": 1,
      "default": 3,
      "label": "Columns on tablet"
    },
    {
      "type": "range",
      "id": "columns_mobile",
      "min": 1,
      "max": 3,
      "step": 1,
      "default": 2,
      "label": "Columns on mobile"
    },
    {
      "type": "header",
      "content": "Color swatches on cards"
    },
    {
      "type": "checkbox",
      "id": "show_swatches_on_cards",
      "label": "Show color swatches on product cards",
      "default": true,
      "info": "Display color options as swatches on collection cards"
    },
    {
      "type": "checkbox",
      "id": "enable_card_hover_preview",
      "label": "Enable card hover preview",
      "default": true,
      "info": "Hover over color swatches to preview variant images"
    },
    {
      "type": "range",
      "id": "max_swatches_per_product",
      "min": 3,
      "max": 8,
      "step": 1,
      "default": 5,
      "label": "Maximum swatches per product",
      "info": "Limit number of color swatches shown on each card"
    },
    {
      "type": "select",
      "id": "swatch_display_style",
      "label": "Swatch display style",
      "options": [
        {
          "value": "below_card",
          "label": "Below card (always visible)"
        },
        {
          "value": "hover_overlay",
          "label": "Hover overlay (show on card hover)"
        }
      ],
      "default": "below_card",
      "info": "Choose how variant options are displayed"
    },
    {
      "type": "select",
      "id": "card_click_behavior",
      "label": "Swatch click behavior",
      "default": "link_with_variant",
      "options": [
        {
          "value": "link_with_variant",
          "label": "Go to product with variant selected"
        },
        {
          "value": "quick_add_variant",
          "label": "Add to cart via quick add"
        }
      ],
      "info": "How swatch clicks behave on collection cards"
    },
    {
      "type": "header",
      "content": "Card appearance"
    },
    {
      "type": "select",
      "id": "card_image_aspect_ratio",
      "label": "Card image aspect ratio",
      "default": "3/4",
      "options": [
        {
          "value": "3/4",
          "label": "Standard portrait (3:4)"
        },
        {
          "value": "4/5",
          "label": "Tall portrait (4:5)"
        },
        {
          "value": "1/1",
          "label": "Square (1:1)"
        },
        {
          "value": "5/4",
          "label": "Landscape (5:4)"
        },
        {
          "value": "16/9",
          "label": "Widescreen (16:9)"
        }
      ],
      "info": "Controls how product images are cropped. Portrait ratios work best for clothing."
    },
    {
      "type": "header",
      "content": "Quick add"
    },
    {
      "type": "checkbox",
      "id": "enable_quick_add",
      "label": "Enable quick add to cart",
      "default": false,
      "info": "Show quick add buttons on product cards"
    },
    {
      "type": "header",
      "content": "Product features"
    },
    {
      "type": "checkbox",
      "id": "enable_favorites",
      "label": "Enable favorites",
      "default": true,
      "info": "Show heart icon on product cards for saving favorites"
    },
    {
      "type": "checkbox",
      "id": "show_new_badges",
      "label": "Show NEW badges",
      "default": true,
      "info": "Display NEW badge on products created within the last 30 days"
    }
  ],
  "blocks": [
    {
      "type": "media_highlight",
      "name": "Media Highlight",
      "limit": 5,
      "settings": [
        {
          "type": "header",
          "content": "Positioning"
        },
        {
          "type": "range",
          "id": "grid_position",
          "min": 0,
          "max": 50,
          "step": 1,
          "label": "Grid position",
          "default": 0,
          "info": "Position in the grid where this media should appear. 0 = end of collection. This media block will span 2 columns."
        },
        {
          "type": "header",
          "content": "Media Content"
        },
        {
          "type": "image_picker",
          "id": "image",
          "label": "Image"
        },
        {
          "type": "video",
          "id": "video",
          "label": "Video",
          "info": "Video will take priority over image if both are provided"
        },
        {
          "type": "text",
          "id": "image_alt",
          "label": "Image alt text",
          "info": "Describe the image for accessibility"
        },
        {
          "type": "text",
          "id": "video_alt",
          "label": "Video description",
          "info": "Describe the video content for accessibility"
        },
        {
          "type": "select",
          "id": "aspect_ratio",
          "label": "Aspect ratio",
          "default": "16:9",
          "options": [
            {
              "value": "1:1",
              "label": "Square (1:1)"
            },
            {
              "value": "4:3",
              "label": "Standard (4:3)"
            },
            {
              "value": "3:4",
              "label": "Portrait (3:4)"
            },
            {
              "value": "16:9",
              "label": "Widescreen (16:9)"
            },
            {
              "value": "9:16",
              "label": "Vertical (9:16)"
            }
          ]
        },
        {
          "type": "select",
          "id": "object_position",
          "label": "Media position",
          "default": "center",
          "options": [
            {
              "value": "center top",
              "label": "Top center"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "center bottom",
              "label": "Bottom center"
            },
            {
              "value": "left center",
              "label": "Left center"
            },
            {
              "value": "right center",
              "label": "Right center"
            }
          ]
        },
        {
          "type": "header",
          "content": "Overlay Content"
        },
        {
          "type": "text",
          "id": "heading",
          "label": "Heading"
        },
        {
          "type": "richtext",
          "id": "text",
          "label": "Text"
        },
        {
          "type": "text",
          "id": "button_label",
          "label": "Button label"
        },
        {
          "type": "url",
          "id": "button_link",
          "label": "Button link"
        }
      ]
    }
  ]
}
{% endschema %}
