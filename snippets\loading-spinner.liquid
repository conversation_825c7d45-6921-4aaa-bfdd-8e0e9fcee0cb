{%- comment -%}
  Loading Spinner Icon
  Simple SVG spinner for loading states
{%- endcomment -%}

<svg class="spinner" aria-hidden="true" focusable="false" width="16" height="16" viewBox="0 0 16 16">
  <circle class="path" cx="8" cy="8" r="7" fill="none" stroke="currentColor" stroke-opacity="0.2" stroke-width="2"></circle>
  <circle class="path" cx="8" cy="8" r="7" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-dasharray="30 85" stroke-dashoffset="0">
    <animateTransform attributeName="transform" type="rotate" values="0 8 8;360 8 8" dur="1s" repeatCount="indefinite"/>
  </circle>
</svg>

<style>
  .spinner {
    animation: spin 1s linear infinite;
    width: 1rem;
    height: 1rem;
  }
  
  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
</style>
