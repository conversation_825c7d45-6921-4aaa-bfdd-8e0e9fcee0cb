{%- comment -%}
  Two-Column Hero (image/video)
  - Edge-to-edge, no gaps
  - Supports image or video per column
  - Optional central overlay content (title + up to 2 links)
{%- endcomment -%}

{%- liquid
  assign section_id = section.id
  assign height_desktop = section.settings.height_desktop
  assign height_mobile = section.settings.height_mobile
-%}

<section id="hero-split-{{ section_id }}" class="hero-split" data-section-type="hero-split">
  <div class="hero-split__grid">
    <div class="hero-split__col hero-split__col--left">
      {% if section.settings.left_media_type == 'video' and section.settings.left_video != blank %}
        {{ section.settings.left_video | video_tag: autoplay: true, loop: true, muted: true, controls: false, playsinline: true, class: 'hero-split__media hero-split__video' }}
      {% elsif section.settings.left_image != blank %}
        {{ section.settings.left_image | image_url: width: 2400 | image_tag: class: 'hero-split__media', loading: 'eager', fetchpriority: 'high' }}
      {% else %}
        <div class="hero-split__placeholder" aria-hidden="true"></div>
      {% endif %}
      {% if section.settings.left_headline != blank or section.settings.left_cta_1_label != blank or section.settings.left_cta_2_label != blank or section.settings.left_subhead != blank %}
        <div class="hero-split__overlay hero-split__overlay--{{ section.settings.left_overlay_position }} {% if section.settings.left_overlay_light_text %}is-light{% else %}is-dark{% endif %}">
          {% if section.settings.left_headline != blank %}<h2 class="hero-split__title">{{ section.settings.left_headline }}</h2>{% endif %}
          {% if section.settings.left_subhead != blank %}<p class="hero-split__subhead">{{ section.settings.left_subhead }}</p>{% endif %}
          <div class="hero-split__actions">
            {% if section.settings.left_cta_1_label != blank and section.settings.left_cta_1_url != blank %}
              <a class="btn btn--primary hero-split__btn" href="{{ section.settings.left_cta_1_url }}">{{ section.settings.left_cta_1_label }}</a>
            {% endif %}
            {% if section.settings.left_cta_2_label != blank and section.settings.left_cta_2_url != blank %}
              <a class="btn btn--secondary hero-split__btn" href="{{ section.settings.left_cta_2_url }}">{{ section.settings.left_cta_2_label }}</a>
            {% endif %}
          </div>
        </div>
      {% endif %}
    </div>

    <div class="hero-split__col hero-split__col--right">
      {% if section.settings.right_media_type == 'video' and section.settings.right_video != blank %}
        {{ section.settings.right_video | video_tag: autoplay: true, loop: true, muted: true, controls: false, playsinline: true, class: 'hero-split__media hero-split__video' }}
      {% elsif section.settings.right_image != blank %}
        {{ section.settings.right_image | image_url: width: 2400 | image_tag: class: 'hero-split__media', loading: 'eager' }}
      {% else %}
        <div class="hero-split__placeholder" aria-hidden="true"></div>
      {% endif %}
      {% if section.settings.right_headline != blank or section.settings.right_cta_1_label != blank or section.settings.right_cta_2_label != blank or section.settings.right_subhead != blank %}
        <div class="hero-split__overlay hero-split__overlay--{{ section.settings.right_overlay_position }} {% if section.settings.right_overlay_light_text %}is-light{% else %}is-dark{% endif %}">
          {% if section.settings.right_headline != blank %}<h2 class="hero-split__title">{{ section.settings.right_headline }}</h2>{% endif %}
          {% if section.settings.right_subhead != blank %}<p class="hero-split__subhead">{{ section.settings.right_subhead }}</p>{% endif %}
          <div class="hero-split__actions">
            {% if section.settings.right_cta_1_label != blank and section.settings.right_cta_1_url != blank %}
              <a class="btn btn--primary hero-split__btn" href="{{ section.settings.right_cta_1_url }}">{{ section.settings.right_cta_1_label }}</a>
            {% endif %}
            {% if section.settings.right_cta_2_label != blank and section.settings.right_cta_2_url != blank %}
              <a class="btn btn--secondary hero-split__btn" href="{{ section.settings.right_cta_2_url }}">{{ section.settings.right_cta_2_label }}</a>
            {% endif %}
          </div>
        </div>
      {% endif %}
    </div>
  </div>
</section>

{% style %}
#hero-split-{{ section_id }}{
  --hero-h-desktop: {% if height_desktop == 'screen' %}100vh{% elsif height_desktop == 'tall' %}90vh{% else %}75vh{% endif %};
  --hero-h-mobile:  {% if height_mobile == 'screen' %}100vh{% elsif height_mobile == 'tall' %}80vh{% else %}60vh{% endif %};
  position: relative;
}
#hero-split-{{ section_id }} .hero-split__grid{display:grid;grid-template-columns:1fr;gap:0;width:100vw;margin-left:50%;transform:translateX(-50%);}
#hero-split-{{ section_id }} .hero-split__col{position:relative;overflow:hidden;height:var(--hero-h-mobile);} 
#hero-split-{{ section_id }} .hero-split__media{position:absolute;inset:0;width:100%;height:100%;object-fit:cover;}
#hero-split-{{ section_id }} .hero-split__video{background:#000;}
#hero-split-{{ section_id }} .hero-split__placeholder{position:absolute;inset:0;background:#eaeaea;}
#hero-split-{{ section_id }} .hero-split__overlay{position:absolute;z-index:3;display:flex;flex-direction:column;align-items:flex-start;gap:.75rem;text-shadow:0 1px 2px rgba(0,0,0,.35);} 
#hero-split-{{ section_id }} .hero-split__overlay.is-light{color:#fff;}
#hero-split-{{ section_id }} .hero-split__overlay.is-dark{color:#111; text-shadow:none;}
#hero-split-{{ section_id }} .hero-split__overlay--bottom-left{left:4%;bottom:4%;}
#hero-split-{{ section_id }} .hero-split__overlay--bottom-center{left:50%;bottom:4%;transform:translateX(-50%);} 
#hero-split-{{ section_id }} .hero-split__overlay--bottom-right{right:4%;bottom:4%;align-items:flex-end;}
#hero-split-{{ section_id }} .hero-split__overlay--middle-left{left:4%;top:50%;transform:translateY(-50%);} 
#hero-split-{{ section_id }} .hero-split__overlay--middle-center{left:50%;top:50%;transform:translate(-50%,-50%);align-items:center;text-align:center;} 
#hero-split-{{ section_id }} .hero-split__overlay--middle-right{right:4%;top:50%;transform:translateY(-50%);align-items:flex-end;} 
#hero-split-{{ section_id }} .hero-split__overlay--top-left{left:4%;top:6%;} 
#hero-split-{{ section_id }} .hero-split__overlay--top-center{left:50%;top:6%;transform:translateX(-50%);text-align:center;align-items:center;} 
#hero-split-{{ section_id }} .hero-split__overlay--top-right{right:4%;top:6%;align-items:flex-end;} 
#hero-split-{{ section_id }} .hero-split__title{font-size:clamp(1.25rem,2.4vw,2rem);margin:0;font-weight:500;letter-spacing:.2px;}
#hero-split-{{ section_id }} .hero-split__subhead{margin:.25rem 0 0 0;opacity:.92;}
#hero-split-{{ section_id }} .hero-split__actions{display:flex;gap:.5rem;margin-top:.75rem;flex-wrap:wrap;justify-content:center;}
@media (min-width: 990px){
  #hero-split-{{ section_id }} .hero-split__grid{grid-template-columns:1fr 1fr;}
  #hero-split-{{ section_id }} .hero-split__col{height:var(--hero-h-desktop);} 
}
{% endstyle %}


{% schema %}
{
  "name": "Hero – Split (2 columns)",
  "settings": [
    { "type": "select", "id": "height_desktop", "label": "Desktop height", "default": "screen", "options": [
      {"value":"screen","label":"Full screen"},
      {"value":"tall","label":"Tall"},
      {"value":"medium","label":"Medium"}
    ]},
    { "type": "select", "id": "height_mobile", "label": "Mobile height", "default": "tall", "options": [
      {"value":"screen","label":"Full screen"},
      {"value":"tall","label":"Tall"},
      {"value":"medium","label":"Medium"}
    ]},

    { "type": "header", "content": "Left column" },
    { "type": "select", "id": "left_media_type", "label": "Media type", "default": "image", "options": [
      {"value":"image","label":"Image"},
      {"value":"video","label":"Video"}
    ]},
    { "type": "image_picker", "id": "left_image", "label": "Image" },
    { "type": "video", "id": "left_video", "label": "Video" },

    { "type": "header", "content": "Right column" },
    { "type": "select", "id": "right_media_type", "label": "Media type", "default": "image", "options": [
      {"value":"image","label":"Image"},
      {"value":"video","label":"Video"}
    ]},
    { "type": "image_picker", "id": "right_image", "label": "Image" },
    { "type": "video", "id": "right_video", "label": "Video" },

    { "type": "header", "content": "Left overlay" },
    { "type": "text", "id": "left_headline", "label": "Left heading" },
    { "type": "text", "id": "left_subhead", "label": "Left subheading" },
    { "type": "select", "id": "left_overlay_position", "label": "Left overlay position", "default": "bottom-left", "options": [
      {"value":"bottom-left","label":"Bottom left"},
      {"value":"bottom-center","label":"Bottom center"},
      {"value":"bottom-right","label":"Bottom right"},
      {"value":"middle-left","label":"Middle left"},
      {"value":"middle-center","label":"Middle center"},
      {"value":"middle-right","label":"Middle right"},
      {"value":"top-left","label":"Top left"},
      {"value":"top-center","label":"Top center"},
      {"value":"top-right","label":"Top right"}
    ]},
    { "type": "checkbox", "id": "left_overlay_light_text", "label": "Light (white) text", "default": true },
    { "type": "text", "id": "left_cta_1_label", "label": "Left primary link label", "default": "Shop now" },
    { "type": "url",  "id": "left_cta_1_url",   "label": "Left primary link URL" },
    { "type": "text", "id": "left_cta_2_label", "label": "Left secondary link label" },
    { "type": "url",  "id": "left_cta_2_url",   "label": "Left secondary link URL" },

    { "type": "header", "content": "Right overlay" },
    { "type": "text", "id": "right_headline", "label": "Right heading" },
    { "type": "text", "id": "right_subhead", "label": "Right subheading" },
    { "type": "select", "id": "right_overlay_position", "label": "Right overlay position", "default": "bottom-left", "options": [
      {"value":"bottom-left","label":"Bottom left"},
      {"value":"bottom-center","label":"Bottom center"},
      {"value":"bottom-right","label":"Bottom right"},
      {"value":"middle-left","label":"Middle left"},
      {"value":"middle-center","label":"Middle center"},
      {"value":"middle-right","label":"Middle right"},
      {"value":"top-left","label":"Top left"},
      {"value":"top-center","label":"Top center"},
      {"value":"top-right","label":"Top right"}
    ]},
    { "type": "checkbox", "id": "right_overlay_light_text", "label": "Light (white) text", "default": true },
    { "type": "text", "id": "right_cta_1_label", "label": "Right primary link label", "default": "Discover" },
    { "type": "url",  "id": "right_cta_1_url",   "label": "Right primary link URL" },
    { "type": "text", "id": "right_cta_2_label", "label": "Right secondary link label" },
    { "type": "url",  "id": "right_cta_2_url",   "label": "Right secondary link URL" },
  ],
  "blocks": [],
  "presets": [
    { "name": "Hero – Split (2 columns)", "category": "Hero" }
  ],
  "disabled_on": { "groups": ["header","footer"] }
}
{% endschema %}
