[{"name": "theme_info", "theme_name": "Skeleton", "theme_version": "0.1.0", "theme_author": "Shopify", "theme_documentation_url": "https://help.shopify.com/manual/online-store/themes", "theme_support_url": "https://support.shopify.com/"}, {"name": "t:general.typography", "settings": [{"type": "header", "content": "t:general.fonts"}, {"type": "font_picker", "id": "type_primary_font", "default": "jost_n4", "label": "t:general.primary"}]}, {"name": "t:general.layout", "settings": [{"type": "select", "id": "max_page_width", "label": "t:labels.page_width", "options": [{"value": "90rem", "label": "t:options.page_width.narrow"}, {"value": "110rem", "label": "t:options.page_width.wide"}], "default": "90rem"}, {"type": "range", "id": "min_page_margin", "min": 10, "max": 100, "step": 1, "unit": "px", "label": "t:labels.page_margin", "default": 20}, {"type": "checkbox", "id": "show_compare_price", "label": "Show compare-at (struck) price", "default": true}, {"type": "checkbox", "id": "show_sale_badge", "label": "Show sale badge", "default": true}]}, {"name": "t:general.color_schemes", "settings": [{"type": "color_scheme_group", "id": "color_schemes", "role": {"background": "background", "text": "text", "primary_button": "solid_button_background", "on_primary_button": "solid_button_label", "secondary_button": "background", "on_secondary_button": "outline_button_label", "primary_button_border": "solid_button_background", "secondary_button_border": "outline_button_label", "links": "accent_1", "icons": "text"}, "definition": [{"type": "color", "id": "background", "label": "t:settings_schema.colors.settings.background.label", "default": "#FFFFFF"}, {"type": "color", "id": "background_gradient", "label": "t:settings_schema.colors.settings.background_gradient.label"}, {"type": "color", "id": "text", "label": "t:settings_schema.colors.settings.text.label", "default": "#121212"}, {"type": "color", "id": "accent_1", "label": "t:settings_schema.colors.settings.accent_1.label", "default": "#121212"}, {"type": "color", "id": "accent_2", "label": "t:settings_schema.colors.settings.accent_2.label", "default": "#334FB4"}, {"type": "color", "id": "outline_button_label", "label": "t:settings_schema.colors.settings.outline_button_label.label", "default": "#121212"}, {"type": "color", "id": "solid_button_background", "label": "t:settings_schema.colors.settings.solid_button_background.label", "default": "#121212"}, {"type": "color", "id": "solid_button_label", "label": "t:settings_schema.colors.settings.solid_button_label.label", "default": "#FFFFFF"}]}]}, {"name": "t:general.colors", "settings": [{"type": "range", "id": "input_corner_radius", "min": 0, "max": 10, "step": 1, "unit": "px", "label": "t:labels.input_corner_radius", "default": 4}]}, {"name": "Header", "settings": [{"type": "checkbox", "id": "header_transparent_overlay", "label": "Enable transparent header overlay", "default": false, "info": "Let the header sit transparently over page banners and heroes."}, {"type": "checkbox", "id": "overlay_disable_section_spacing", "label": "Prada style: remove spacing between sections", "default": false, "info": "Removes vertical gaps between sections. Recommended with transparent header overlay for a luxury, no-gap layout."}, {"type": "checkbox", "id": "header_black_on_pages", "label": "Black header on non-home pages", "default": false, "info": "Display header with black text/icons on white background for all pages except home page."}, {"type": "select", "id": "header_sticky_behavior", "label": "Sticky navigation behavior", "default": "inherit", "options": [{"value": "inherit", "label": "Use section settings"}, {"value": "none", "label": "None - header scrolls normally"}, {"value": "always", "label": "Always sticky"}, {"value": "on-scroll-up", "label": "Show on scroll up"}], "info": "Override the header section sticky options globally. When set to 'Use section settings', the header section controls will be used."}]}, {"name": "Products", "settings": [{"type": "header", "content": "<PERSON><PERSON>"}, {"type": "checkbox", "id": "enable_cart_drawer", "label": "Enable cart drawer", "default": true, "info": "Show cart as a slide-out drawer instead of navigating to cart page"}]}]