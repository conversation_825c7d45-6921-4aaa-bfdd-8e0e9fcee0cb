{%- comment -%}
  Prada-Style Product Page
  
  Purpose: Clean, sophisticated product page with image slider and dot indicators
  - Image slider with dot navigation on the left
  - Product details and variant selection on the right
  - Minimalist design inspired by luxury fashion sites
  - Color swatch support with hover preview
{%- endcomment -%}

{%- liquid
  assign classes = ''
  case section.settings.layout_style
    when '50-50'  then assign classes = classes | append: ' product--layout-50-50'
    when '60-40'  then assign classes = classes | append: ' product--layout-60-40'
  endcase
  if section.settings.gallery_two_cols
    assign classes = classes | append: ' product--gallery-2cols'
  endif
-%}

<style>
  /* Prevent horizontal scroll on the page */
  body {
    overflow-x: hidden;
  }
  
    .product-page {
    display: grid;
    grid-template-columns: 1fr;
    gap: 2rem;
    max-width: 100vw;
    width: 100%;
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }
  
  @media (min-width: 768px) {
    .product-page {
      grid-template-columns: 1fr 1fr;
      gap: 0;
      max-width: 100vw;
      padding: 0;
      min-height: 100vh;
      overflow-x: hidden; /* Prevent horizontal scroll */
    }
  }
  
  /* Adjust layout for wide screens to better accommodate 5:7 images */
  @media (min-width: 1200px) {
    .product-page {
      grid-template-columns: 60% 40%; /* Give more space to images on wide screens */
    }
  }
  
  @media (min-width: 1600px) {
    .product-page {
      grid-template-columns: 65% 35%; /* Even more space for images on ultra-wide screens */
    }
  }
  
  /* Image Slider Section */
  .product-media {
    position: relative;
    display: flex;
    flex-direction: column;
    height: auto;
  }
  
  @media (min-width: 768px) {
    .product-media {
      width: 100%; /* Take full width of its grid column */
      height: 100vh;
      position: sticky;
      top: 0;
      display: block; /* Reset to block for desktop */
    }
  }
  
  /* Ensure media takes proper space on wide screens */
  @media (min-width: 1200px) {
    .product-media {
      width: 100%;
      max-width: none;
    }
  }
  
  .product-slider {
    position: relative;
    overflow: hidden;
    background: #f8f8f8;
    cursor: grab;
    user-select: none;
    -webkit-user-select: none;
    touch-action: pan-y;
    width: 100%;
    height: calc(100vh - var(--summary-height, 120px)); /* Dynamic height based on summary */
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  /* Enable horizontal swiping for swipe animation on mobile */
  @media (max-width: 989px) {
    .product-slider.swipe-animation {
      touch-action: pan-x pan-y;
    }
  }
  
 @media (min-width: 768px) {
    .product-slider {
      height: 100vh; /* Full height on desktop */
      position: relative;
      min-height: 600px; /* Minimum height to prevent too small containers */
      width: 100%; /* Full width of its container */
    }
  }
  
  /* Ensure slider takes exactly 50% of page width on desktop */
  @media (min-width: 768px) {
    .product-page {
      grid-template-columns: 1fr 1fr; /* Two equal columns */
    }
  }
  
  /* Optimize for wide screens and 5:7 image ratio */
  @media (min-width: 1200px) {
    .product-slider {
      height: 100vh;
      max-height: none; /* Remove height cap for better image display */
    }
  }
  
  /* For ultra-wide screens, use full height */
  @media (min-width: 1920px) {
    .product-slider {
      height: 100vh; /* Use full viewport height */
      max-height: none; /* Remove max-height constraint */
    }
  }
  
  .product-slider:active {
    cursor: grabbing;
  }
  
  /* Slider Container for Scroll Animation */
  .slider-container {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
  }
  
  .slider-track {
    width: 100%;
    max-width: 100%;
    height: 100%;
    transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    box-sizing: border-box;
  }
  
  /* Fade Animation (Default) */
  .slider-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover; /* Use cover for full width display */
    object-position: center;
    opacity: 0;
    transition: opacity 0.4s ease;
    background: #f8f8f8; /* Match container background */
  }
  
  .slider-image.active {
    opacity: 1;
  }
  
  /* Media query for better image handling on wider screens */
  @media (min-width: 768px) and (max-aspect-ratio: 4/3) {
    .slider-image {
      object-fit: cover; /* Use cover for full width display */
      object-position: center;
    }
  }
  
  @media (min-width: 768px) and (min-aspect-ratio: 4/3) {
    .slider-image {
      object-fit: cover; /* Use cover for full width display on wide screens */
      object-position: center;
    }
  }
  
  /* Two-column layout for fade animation */
  .product-slider.two-column .slider-track {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
  }
  
  .product-slider.two-column .slider-image {
    position: static;
    display: block;
    width: 100%;
    height: auto;
    opacity: 1;
  }
  
  .product-slider.two-column .slider-image.active {
    opacity: 1;
  }
  
  /* Scroll Animation - Vertical Scroll */
  .product-slider.scroll-animation {
    overflow-y: auto;
    overflow-x: hidden;
    scroll-behavior: smooth;
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 255, 255, 0.6) transparent;
  }
  
  /* Custom Scrollbar for Webkit browsers */
  .product-slider.scroll-animation::-webkit-scrollbar {
    width: 4px;
  }
  
  .product-slider.scroll-animation::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
  }
  
  .product-slider.scroll-animation::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.6);
    border-radius: 2px;
    transition: background 0.2s ease;
  }
  
  .product-slider.scroll-animation::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.8);
  }
  
  .product-slider.scroll-animation .slider-container {
    overflow: visible;
  }
  
  .product-slider.scroll-animation .slider-track {
    display: block;
    height: auto;
    transition: none;
  }
  
  .product-slider.scroll-animation .slider-image {
    position: static;
    display: block;
    width: 100%;
    height: calc(100vh - var(--summary-height, 120px)); /* Match container height for mobile */
    opacity: 1;
    transition: none;
    object-fit: cover; /* Use cover for full width display */
    object-position: center;
    background: #f8f8f8;
    margin-bottom: 2px; /* Add 2px space between images */
  }
  
  /* Two-column layout for scroll animation */
  .product-slider.scroll-animation.two-column .slider-track {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
  }
  
  .product-slider.scroll-animation.two-column .slider-image {
    position: static;
    display: block;
    width: 100%;
    height: auto;
    margin-bottom: 0;
  }
  
 @media (min-width: 768px) {
    .product-slider.scroll-animation .slider-image {
      height: auto; /* Full height on desktop */
    }
  }
  
  /* Responsive object-fit for scroll animation */
  @media (min-width: 768px) and (max-aspect-ratio: 4/3) {
    .product-slider.scroll-animation .slider-image {
      object-fit: cover; /* Use cover for full width display */
      object-position: center;
    }
  }
  
  @media (min-width: 768px) and (min-aspect-ratio: 4/3) {
    .product-slider.scroll-animation .slider-image {
      object-fit: cover; /* Use cover for full width display */
      object-position: center;
    }
  }
  
  /* For ultra-wide screens, ensure images extend properly */
  @media (min-width: 1920px) {
    .product-slider.scroll-animation .slider-image {
      height: auto; /* Full viewport height */
      max-height: none; /* Remove height restrictions */
    }
  }
  
  .product-slider.scroll-animation .slider-image.active {
    opacity: 1;
  }
  
  /* Product Summary Below Slider */
  .product-summary {
    padding: 2rem 1.5rem;
    background: white;
    min-height: 120px; /* Minimum height to prevent clipping */
    height: auto; /* Auto height based on content */
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
  
  @media (min-width: 768px) {
    .product-summary {
      display: none; /* Hide on desktop - use right panel instead */
    }
  }
  
  .product-summary .product-title {
    font-size: 1.25rem;
    font-weight: 400;
    margin: 0 0 0.5rem 0;
    letter-spacing: 0.5px;
    text-transform: uppercase;
    word-wrap: break-word;
    overflow-wrap: break-word;
  }
  
  .product-summary .product-price {
    font-size: 1rem;
    margin-bottom: 1.5rem;
    color: #666;
    font-weight: 500;
    word-wrap: break-word;
  }
  
  .product-summary .add-to-cart-btn {
    width: 100%;
    max-width: 100%;
    padding: 1rem;
    background: #000;
    color: white;
    border: none;
    font-size: 0.875rem;
    font-weight: 500;
    letter-spacing: 1px;
    text-transform: uppercase;
    cursor: pointer;
    transition: all 0.2s ease;
    box-sizing: border-box;
  }
  
  .product-summary .add-to-cart-btn:hover {
    background: #333;
  }
  
  .product-summary .add-to-cart-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
  }
  
  /* Dot Indicators */
  .slider-dots {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    position: absolute;
    left: 1.5rem;
    top: 50%;
    transform: translateY(-50%);
    z-index: 10;
  }
  
  @media (min-width: 768px) {
    .slider-dots {
      left: 2rem;
    }
  }
  
  .dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.4);
    border: none;
    cursor: pointer;
    transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
    padding: 0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }
  
  .dot.active {
    background: rgba(255, 255, 255, 0.9);
    transform: scale(1.33);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  }
  
  .dot:hover:not(.active) {
    background: rgba(255, 255, 255, 0.6);
    transform: scale(1.17);
  }
  
  /* Product Info Section */
  .product-info {
    padding: 2rem 1rem;
    width: 100%;
    max-width: 100vw;
    box-sizing: border-box;
  }
  
  @media (min-width: 768px) {
    .product-info {
      width: 100%;
      max-width: none;
      padding: 3rem 2.5rem;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
    }
  }
  
  @media (min-width: 1200px) {
    .product-info {
      padding: 3rem 3rem 3rem 2rem;
    }
  }
  

  
  .product-description {
    line-height: 1.6;
    margin-bottom: 2rem;
    color: #333;
  }
  
  /* Variant Selection */
  .variant-group {
    margin-bottom: 2rem;
  }
  
  .variant-label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    margin-bottom: 0.75rem;
    letter-spacing: 0.5px;
    text-transform: uppercase;
  }
  
  /* Color Swatches */
  .color-swatches {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
  }
  
  .color-swatch {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    border: 1px solid #ddd;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
  }
  
  .color-swatch:hover,
  .color-swatch.selected {
    transform: scale(1.1);
    box-shadow: 0 0 0 2px white, 0 0 0 3px #000;
  }
  
  /* Size Selection */
  .size-options {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
  }
  
  .size-option {
    padding: 0.5rem 1rem;
    border: 1px solid #ddd;
    background: white;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.875rem;
  }
  
  .size-option:hover,
  .size-option.selected {
    border-color: #000;
    background: #000;
    color: white;
  }
  
  /* Add to Cart */
  .add-to-cart-section {
    margin-top: 2rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }
  
  .add-to-cart-btn {
    width: 100%;
    padding: 1rem 2rem;
    background: #000;
    color: white;
    border: none;
    font-size: 0.875rem;
    font-weight: 500;
    letter-spacing: 0.1em;
    text-transform: uppercase;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
  }
  
  .add-to-cart-btn:hover {
    background: #333;
  }
  
  .add-to-cart-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
  }

  /* Enhanced Product Info Styles */
  .product-badge {
    display: inline-block;
    font-size: 0.75rem;
    font-weight: 600;
    letter-spacing: 0.1em;
    text-transform: uppercase;
    color: #000;
    margin-bottom: 0.75rem;
  }

  .product-info .product-title {
    font-size: 1.5rem;
    font-weight: 400;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin: 0 0 0.75rem 0;
    line-height: 1.2;
  }

  .product-color {
    font-size: 0.875rem;
    color: #666;
    margin-bottom: 1.25rem;
  }

  .color-label {
    font-weight: 500;
  }

  /* Product Status */
  .product-status {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
  }

  .stock-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
  }

  .stock-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    flex-shrink: 0;
  }

  .stock-indicator.in-stock {
    background-color: #22c55e;
  }

  .stock-indicator.out-of-stock {
    background-color: #ef4444;
  }

  .points-program {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: #0066cc;
  }

  .points-icon {
    color: #0066cc;
    flex-shrink: 0;
  }

  /* Enhanced Price Display */
  .product-info .product-price {
    margin-bottom: 1rem;
  }

  .product-info .price {
    font-size: 1.25rem;
    font-weight: 600;
    color: #000;
  }

  .product-info .compare-price {
    font-size: 1rem;
    color: #999;
    text-decoration: line-through;
    margin-left: 0.5rem;
  }

  .pricing-notice {
    font-size: 0.75rem;
    color: #666;
    margin-bottom: 1.5rem;
    line-height: 1.4;
  }

  .pricing-notice p {
    margin: 0 0 0.25rem 0;
  }

  .learn-more-link {
    color: #0066cc;
    text-decoration: underline;
  }

  /* Wishlist Button */
  .wishlist-btn {
    width: 100%;
    padding: 1rem 2rem;
    background: transparent;
    color: #000;
    border: 1px solid #ddd;
    font-size: 0.875rem;
    font-weight: 500;
    letter-spacing: 0.1em;
    text-transform: uppercase;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    margin-top: 1rem;
  }

  .wishlist-btn:hover {
    border-color: #000;
  }

  /* Shipping Information */
  .shipping-info {
    margin: 2rem 0;
    padding: 1.5rem;
    background: #f9f9f9;
    border-radius: 4px;
  }

  .shipping-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.75rem;
    font-size: 0.875rem;
  }

  .shipping-item:last-child {
    margin-bottom: 0;
  }

  .shipping-icon {
    font-size: 1rem;
  }

  /* Collapsible Product Details */
  .product-details-accordion {
    margin-top: 2rem;
    border-top: 1px solid #e5e5e5;
  }

  .product-detail-section {
    border-bottom: 1px solid #e5e5e5;
  }

  .detail-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.25rem 0;
    font-size: 0.875rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    cursor: pointer;
    list-style: none;
  }

  .detail-header::-webkit-details-marker {
    display: none;
  }

  .detail-toggle {
    transition: transform 0.2s ease;
    font-size: 0.75rem;
  }

  .product-detail-section[open] .detail-toggle {
    transform: rotate(180deg);
  }

  .detail-content {
    padding-bottom: 1.25rem;
    font-size: 0.875rem;
    line-height: 1.6;
    color: #666;
  }

  /* Hidden radio inputs */
  .variant-input {
    position: absolute;
    opacity: 0;
    pointer-events: none;
  }

  @media (max-width: 767px) {
    .slider-dots {
      flex-direction: row;
      position: static;
      justify-content: center;
      margin-top: 1.5rem;
      transform: none;
      gap: 1rem;
    }
    
    .dot {
      width: 8px;
      height: 8px;
    }
    
    .product-slider::after {
      content: '';
      position: absolute;
      bottom: 1rem;
      right: 1rem;
      width: 20px;
      height: 20px;
      background: rgba(255, 255, 255, 0.8);
      border-radius: 50%;
      background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%23666'%3e%3cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M7 16l3-3m0 0l3 3m-3-3v6m0-6l3-3m-3 3L7 7'/%3e%3c/svg%3e");
      background-size: 12px;
      background-repeat: no-repeat;
      background-position: center;
      opacity: 0.6;
      pointer-events: none;
      animation: swipeHint 3s infinite;
    }
  }
  
  @keyframes swipeHint {
    0%, 80%, 100% { 
      opacity: 0.6; 
      transform: scale(1);
    }
    10%, 70% { 
      opacity: 0.8; 
      transform: scale(1.1);
    }
  }

  /* New Grid Layout Styles */
  #shopify-section-{{ section.id }} .product__grid { 
    display: grid; 
    gap: 24px; 
  }
  
  /* Debug: Force grid when any layout class is present */
  .product--layout-50-50 .product__grid,
  .product--layout-60-40 .product__grid {
    display: grid !important;
    gap: 24px !important;
  }

  @media (min-width: 768px) {
    /* Override the existing product-page grid when using new layouts */
    #shopify-section-{{ section.id }} .product--layout-50-50 .product-page,
    #shopify-section-{{ section.id }} .product--layout-60-40 .product-page {
      display: block !important;
      grid-template-columns: none !important;
      gap: 0 !important;
      min-height: auto !important;
    }
    
    /* Force the new grid layouts */
    #shopify-section-{{ section.id }} .product--layout-50-50 .product__grid {
      display: grid !important;
      grid-template-columns: minmax(0, 1fr) minmax(0, 1fr) !important;
      align-items: start;
      gap: 24px;
    }
    
    #shopify-section-{{ section.id }} .product--layout-60-40 .product__grid {
      display: grid !important;
      grid-template-columns: minmax(0, 3fr) minmax(0, 2fr) !important; /* ≈60/40 */
      align-items: start;
      gap: 24px;
    }
    
    /* Ensure media and info containers don't have conflicting styles */
    #shopify-section-{{ section.id }} .product--layout-50-50 .product__media,
    #shopify-section-{{ section.id }} .product--layout-60-40 .product__media {
      width: 100%;
      max-width: none;
      height: auto;
    }
    
    #shopify-section-{{ section.id }} .product--layout-50-50 .product__info,
    #shopify-section-{{ section.id }} .product--layout-60-40 .product__info {
      width: 100%;
      max-width: none;
      height: auto;
      padding: 2rem;
    }
    
    /* Reset any height constraints from the original design */
    #shopify-section-{{ section.id }} .product--layout-50-50 .product-media,
    #shopify-section-{{ section.id }} .product--layout-60-40 .product-media {
      height: auto !important;
      min-height: 400px;
      position: relative !important;
    }
    
    /* Optional sticky info */
    #shopify-section-{{ section.id }} .product__info { 
      position: sticky; 
      top: 24px; 
    }
    
    /* Fallback rules without section ID dependency */
    .product--layout-50-50 .product__grid {
      display: grid !important;
      grid-template-columns: minmax(0, 1fr) minmax(0, 1fr) !important;
      align-items: start;
      gap: 24px;
    }
    
    .product--layout-60-40 .product__grid {
      display: grid !important;
      grid-template-columns: minmax(0, 3fr) minmax(0, 2fr) !important;
      align-items: start;
      gap: 24px;
    }
    
    /* Reset conflicting styles */
    .product--layout-50-50 .product-page,
    .product--layout-60-40 .product-page {
      display: block !important;
      grid-template-columns: none !important;
      gap: 0 !important;
      min-height: auto !important;
    }
  }

  /* 2-column gallery (non-destructive to slider) */
  @media (min-width: 990px) {
    #shopify-section-{{ section.id }} .product--gallery-2cols .slider-track {
      display: grid !important;
      grid-template-columns: repeat(2, minmax(0, 1fr));
      gap: 12px;
    }
    
    #shopify-section-{{ section.id }} .product--gallery-2cols .slider-container { 
      display: contents; 
    }
    
    #shopify-section-{{ section.id }} .product--gallery-2cols .slider-image { 
      width: auto !important;
      position: static;
      opacity: 1;
    }
  }
</style>

{% style %}
@media (min-width: 990px){
  /* Make the split happen on the actual grid container */
  #product-{{ section.id }} .product--layout-50-50 .product__grid{
    display:grid;
    grid-template-columns:minmax(0,1fr) minmax(0,1fr);
    align-items:start;
    gap:24px;
  }
  #product-{{ section.id }} .product--layout-60-40 .product__grid{
    display:grid;
    grid-template-columns:minmax(0,3fr) minmax(0,2fr); /* ≈60/40 */
    align-items:start;
    gap:24px;
  }

  /* Neutralize the inner page grid so it can't fight the outer split */
  #product-{{ section.id }} .product--layout-50-50 .product-page,
  #product-{{ section.id }} .product--layout-60-40 .product-page{
    display:block !important;
    grid-template-columns:initial !important;
    min-height:auto !important;
    gap:0 !important;
  }

  /* Prevent inner containers from imposing heights that force stacking */
  #product-{{ section.id }} .product--layout-50-50 .product-media,
  #product-{{ section.id }} .product--layout-60-40 .product-media{
    height:auto !important;
    position:relative !important;
  }
  #product-{{ section.id }} .product__info{ position:sticky; top:24px; }
}
{% endstyle %}

{% style %}
@media (min-width: 990px){
  /* Apply the 2-col split on the actual grid container */
  .product[data-layout="50-50"] > .product__grid{
    display:grid;
    grid-template-columns: minmax(0,1fr) minmax(0,1fr);
    align-items:start;
    gap:24px;
  }
  .product[data-layout="60-40"] > .product__grid{
    display:grid;
    grid-template-columns: minmax(0,3fr) minmax(0,2fr); /* ≈60/40 */
    align-items:start;
    gap:24px;
  }

  /* Neutralize the inner page/grid so it can't override the split */
  .product[data-layout] .product-page{
    display:block !important;
    grid-template-columns: initial !important;
    gap:0 !important;
    min-height:auto !important;
  }
  .product[data-layout] .product-media{
    position:relative !important;
    height:auto !important;
  }

  /* Optional sticky info */
  .product[data-layout] .product__info{ position:sticky; top:24px; }
}
{% endstyle %}

{% style %}
@media (min-width: 990px){
  /* Hide dots on desktop when 2-column gallery is active */
  #product-{{ section.id }}.product--gallery-2cols .slider-dots { display: none !important; }
  
  /* Target the same element that holds the gallery flag */
  #product-{{ section.id }}.product--gallery-2cols .product-media{
    position: static !important;
    top: auto !important;
    height: auto !important;
  }
  #product-{{ section.id }}.product--gallery-2cols .product-slider{
    height: auto !important;
    overflow: visible !important;
  }
  #product-{{ section.id }}.product--gallery-2cols .slider-container{
    height: auto !important;
  }
  #product-{{ section.id }}.product--gallery-2cols .slider-track{
    display: grid !important;
    grid-template-columns: repeat(2, minmax(0,1fr));
    gap: 12px;
  }
  #product-{{ section.id }}.product--gallery-2cols .slider-image{
    position: static !important;
    width: 100% !important;
    height: auto !important;
    object-fit: cover;
  }
}
{% endstyle %}

{% style %}
@media (max-width: 749px) {
  /* All mobile styles (dots or segments) – position within the slider */
  #product-{{ section.id }} .product-slider { position: relative; }

  #product-{{ section.id }} .product-slider .slider-dots {
    position: absolute !important;
    left: 50%;
    top: auto;
    bottom: 4%;                 /* ≈4% from the slider bottom */
    transform: translateX(-50%);/* center horizontally */
    margin: 0;
    flex-direction: row;
    gap: 6px;
    z-index: 20;
  }

  /* Hide dots entirely on mobile when merchant chooses 'none' */
  #product-{{ section.id }}.product--mobile-no-pagination .slider-dots { display: none !important; }

  /* Segments look (keep your dash visuals) */
  #product-{{ section.id }}.product--mobile-segments .dot {
    /* This is overridden by the later styles */
  }
  #product-{{ section.id }}.product--mobile-segments .dot.active {
    /* This is overridden by the later styles */
  }

  /* Hover/focus affordance for accessibility */
  #product-{{ section.id }}.product--mobile-segments .dot:focus-visible {
    outline: 2px solid currentColor;
    outline-offset: 2px;
  }
}

/* Respect reduced motion */
@media (prefers-reduced-motion: reduce) {
  #product-{{ section.id }}.product--mobile-segments .dot {
    transition: none;
  }
}
{% endstyle %}

{% style %}
/* Mobile pagination: fixed dash length + tight, consistent gaps */
#product-{{ section.id }} {
  --seg-l: 18px;                          /* dash length (keep original feel) */
  --seg-h: 2px;                           /* dash height */
  --seg-gap: clamp(4px, 1.2vw, 7px);      /* small, consistent gaps */
}

@media (max-width: 749px){
  /* keep dots/segments inside slider and 4% up from bottom */
  #product-{{ section.id }} .product-slider{ position: relative; }
  #product-{{ section.id }} .product-slider .slider-dots{
    position: absolute !important;
    left: 50%;
    bottom: max(4%, env(safe-area-inset-bottom)) !important;
    top: auto !important;
    transform: translateX(-50%);
    gap: var(--seg-gap) !important;
    margin: 0 !important;
  }

  /* segments: same length for all; active = brighter + a hair thicker */
  #product-{{ section.id }} .slider-dots .dot{
    width: var(--seg-l) !important;
    height: var(--seg-h) !important;
    border-radius: 2px !important;
    background: rgba(255,255,255,.45) !important;
    box-shadow: none !important;
    transform: none !important;           /* avoid scale warping gaps */
    padding: 0 !important;
    margin: 0 !important;
  }
  #product-{{ section.id }} .slider-dots .dot.active{
    background: #fff !important;
    height: calc(var(--seg-h) + .5px) !important;  /* subtle emphasis */
  }
}
{% endstyle %}

{% style %}
#product-{{ section.id }} {
  /* Tunables */
  --seg-l: 18px;                 /* inactive length (keep original) */
  --seg-l-active: 30px;          /* active length – a bit longer */
  --seg-h: 2px;                  /* height */
  --seg-gap: clamp(6px, 1.6vw, 10px); /* tight, consistent spacing */
}

@media (max-width: 749px){
  /* Keep pagination inside the slider and 4% from bottom */
  #product-{{ section.id }} .product-slider{ position: relative; }
  #product-{{ section.id }}.product--mobile-segments .slider-dots{
    position: absolute !important;
    left: 50%;
    bottom: max(4%, env(safe-area-inset-bottom)) !important;
    top: auto !important;
    transform: translateX(-50%);
    gap: var(--seg-gap) !important;
    margin: 0 !important;
    z-index: 20;
  }

  /* Dashes with animated width for the active item */
  #product-{{ section.id }}.product--mobile-segments .dot{
    width: var(--seg-l);
    height: var(--seg-h);
    border-radius: var(--seg-h);
    background: rgba(255,255,255,.45);
    box-shadow: none;
    padding: 0; margin: 0;
    transform: none;                /* prevents gap warping */
    will-change: width, background-color, opacity;
    transition:
      width 260ms cubic-bezier(.33,0,.2,1),
      background-color 200ms ease,
      opacity 200ms ease;
  }
  #product-{{ section.id }}.product--mobile-segments .dot.active{
    width: var(--seg-l-active);     /* longer active dash */
    background: #fff;               /* brighter */
    opacity: 1;
  }

  /* Optional: subtle hover/focus */
  #product-{{ section.id }}.product--mobile-segments .dot:focus-visible{
    outline: 2px solid currentColor;
    outline-offset: 2px;
  }
}

/* Respect reduced motion */
@media (prefers-reduced-motion: reduce){
  #product-{{ section.id }}.product--mobile-segments .dot{
    transition: none;
  }
}
{% endstyle %}

{%- liquid
  assign classes = ''
  case section.settings.layout_style
    when '50-50'  then assign classes = classes | append: ' product--layout-50-50'
    when '60-40'  then assign classes = classes | append: ' product--layout-60-40'
  endcase
  if section.settings.gallery_two_cols
    assign classes = classes | append: ' product--gallery-2cols'
  endif
-%}

<div id="product-{{ section.id }}"
     class="product{{ classes }}{% if section.settings.mobile_pagination_style == 'segments' %} product--mobile-segments{% endif %}{% if section.settings.mobile_pagination_style == 'none' %} product--mobile-no-pagination{% endif %}"
     data-layout="{{ section.settings.layout_style }}"
     data-mobile-pagination="{{ section.settings.mobile_pagination_style }}">
  <div class="product__grid">
    <div class="product__media">
      <section class="product-page" data-section="{{ section.id }}">
        <!-- Image Slider Section -->
        <div class="product-media">
          <div class="product-slider{% if section.settings.slider_animation == 'scroll' %} scroll-animation{% elsif section.settings.slider_animation == 'swipe' %} swipe-animation{% endif %}{% if section.settings.two_column_layout %} two-column{% endif %}" id="productSlider">
            <div class="slider-container">
              <div class="slider-track" id="sliderTrack">
                {%- for media in product.media limit: 6 -%}
                  <img 
                    src="{{ media | image_url: width: 800 }}"
                    srcset="{{ media | image_url: width: 400 }} 400w,
                            {{ media | image_url: width: 600 }} 600w,
                            {{ media | image_url: width: 800 }} 800w,
                            {{ media | image_url: width: 1000 }} 1000w"
                    sizes="(min-width: 768px) 50vw, 100vw"
                    alt="{{ media.alt | default: product.title }}"
                    class="slider-image{% if forloop.first %} active{% endif %}"
                    data-media-id="{{ media.id }}"
                    width="{{ media.width | default: 800 }}"
                    height="{{ media.height | default: 1000 }}"
                    {% if forloop.first %}loading="eager"{% else %}loading="lazy"{% endif %}
                  >
                {%- endfor -%}
              </div>
            </div>
            
            {%- if product.media.size > 1 -%}
              <div class="slider-dots">
                {%- for media in product.media limit: 6 -%}
                  <button
                    class="dot{% if forloop.first %} active{% endif %}"
                    data-slide="{{ forloop.index0 }}"
                    aria-label="View image {{ forloop.index }}"
                  ></button>
                {%- endfor -%}
              </div>
            {%- endif -%}
          </div>
          
          <!-- Product Summary Below Slider -->
          <div class="product-summary">
            <h1 class="product-title">{{ product.title | escape }}</h1>
            
            <div class="product-price">
              <span id="productPriceSummary">{{ product.selected_or_first_available_variant.price | money }}</span>
              {%- if product.selected_or_first_available_variant.compare_at_price > product.selected_or_first_available_variant.price -%}
                <span class="original-price" style="text-decoration: line-through; margin-left: 0.5rem; color: #999;">
                  {{ product.selected_or_first_available_variant.compare_at_price | money }}
                </span>
              {%- endif -%}
            </div>

            <button 
              type="button" 
              class="add-to-cart-btn"
              id="addToCartBtnSummary"
              onclick="document.getElementById('addToCartBtn').click()"
              {% unless product.selected_or_first_available_variant.available %}disabled{% endunless %}
            >
              {%- if product.selected_or_first_available_variant.available -%}
                Add to Shopping Bag
              {%- else -%}
                Sold Out
              {%- endif -%}
            </button>
          </div>
        </div>
      </section>
    </div>
    <div class="product__info">
      <!-- Product Info Section -->
      <div class="product-info">
        {%- if section.settings.show_new_badge -%}
          <div class="product-badge">NEW</div>
        {%- endif -%}

        <h1 class="product-title">{{ product.title | escape }}</h1>

        {%- if product.selected_or_first_available_variant.sku != blank -%}
          <div class="product-color">
            <span class="color-label">Color:</span>
            <span class="color-value">{{ product.selected_or_first_available_variant.option1 | default: 'Dusty Purple' }}</span>
          </div>
        {%- endif -%}

        <!-- Stock Status and Points -->
        <div class="product-status">
          {%- if section.settings.show_stock_status -%}
            <div class="stock-status">
              {%- if product.selected_or_first_available_variant.available -%}
                <span class="stock-indicator in-stock"></span>
                <span class="stock-text">In Stock</span>
              {%- else -%}
                <span class="stock-indicator out-of-stock"></span>
                <span class="stock-text">Out of Stock</span>
              {%- endif -%}
            </div>
          {%- endif -%}

          {%- if section.settings.show_points_program -%}
            <div class="points-program">
              <span class="points-icon">★</span>
              <span class="points-text">{{ section.settings.points_text }}</span>
            </div>
          {%- endif -%}
        </div>

        <!-- Price Display -->
        <div class="product-price">
          <span class="price" id="productPrice">{{ product.selected_or_first_available_variant.price | money }}</span>
          {%- if product.selected_or_first_available_variant.compare_at_price > product.selected_or_first_available_variant.price -%}
            <span class="compare-price">{{ product.selected_or_first_available_variant.compare_at_price | money }}</span>
          {%- endif -%}
        </div>

        <!-- Pricing Notice -->
        <div class="pricing-notice">
          <p>All prices are now inclusive of all Canadian customs duties and tariffs</p>
          <a href="#" class="learn-more-link">Learn more</a>
        </div>

        <!-- Product Form -->
        {% form 'product', product, class: 'product-form', id: 'product-form' %}
          {%- for option in product.options_with_values -%}
            {%- liquid
              assign option_name = option.name | downcase
              assign is_color = false
              if option_name contains 'color' or option_name contains 'colour'
                assign is_color = true
              endif
            -%}
            
            <div class="variant-group">
              <label class="variant-label">{{ option.name }}</label>
              
              {%- if is_color -%}
                <div class="color-swatches">
                  {%- for value in option.values -%}
                    <input 
                      type="radio" 
                      name="options[{{ option.name | escape }}]" 
                      value="{{ value | escape }}"
                      id="option-{{ option.position }}-{{ forloop.index0 }}"
                      class="variant-input"
                      {% if option.selected_value == value %}checked{% endif %}
                      data-option-position="{{ option.position }}"
                    >
                    <label 
                      for="option-{{ option.position }}-{{ forloop.index0 }}"
                      class="color-swatch swatch--{{ value | handle }}{% if option.selected_value == value %} selected{% endif %}"
                      style="background-color: {{ value | handle | replace: '-', '' }};"
                      title="{{ value | escape }}"
                    ></label>
                  {%- endfor -%}
                </div>
              {%- else -%}
                <div class="size-options">
                  {%- for value in option.values -%}
                    <input 
                      type="radio" 
                      name="options[{{ option.name | escape }}]" 
                      value="{{ value | escape }}"
                      id="option-{{ option.position }}-{{ forloop.index0 }}"
                      class="variant-input"
                      {% if option.selected_value == value %}checked{% endif %}
                      data-option-position="{{ option.position }}"
                    >
                    <label 
                      for="option-{{ option.position }}-{{ forloop.index0 }}"
                      class="size-option{% if option.selected_value == value %} selected{% endif %}"
                    >
                      {{ value | escape }}
                    </label>
                  {%- endfor -%}
                </div>
              {%- endif -%}
            </div>
          {%- endfor -%}

          <!-- Add to Cart Section -->
          <div class="add-to-cart-section">
            <button
              type="submit"
              name="add"
              class="add-to-cart-btn"
              id="addToCartBtn"
              {% unless product.selected_or_first_available_variant.available %}disabled{% endunless %}
            >
              <span class="btn-icon">🛍</span>
              {%- if product.selected_or_first_available_variant.available -%}
                ADD TO CART
              {%- else -%}
                SOLD OUT
              {%- endif -%}
            </button>

            <button type="button" class="wishlist-btn" id="addToWishlist">
              <span class="wishlist-icon">♡</span>
              ADD TO WISHLIST
            </button>
          </div>

          <!-- Hidden variant input -->
          <input 
            type="hidden" 
            name="id" 
            value="{{ product.selected_or_first_available_variant.id }}"
            id="variantId"
          >
        {% endform %}

        <!-- Shipping Information -->
        {%- if section.settings.show_shipping_info -%}
          <div class="shipping-info">
            <div class="shipping-item">
              <span class="shipping-icon">📦</span>
              <span class="shipping-text">Free standard shipping over {{ section.settings.free_standard_threshold }}</span>
            </div>
            <div class="shipping-item">
              <span class="shipping-icon">⚡</span>
              <span class="shipping-text">Free express shipping over {{ section.settings.free_express_threshold }}</span>
            </div>
            {%- if section.settings.show_easy_returns -%}
              <div class="shipping-item">
                <span class="shipping-icon">↩</span>
                <span class="shipping-text">Easy returns</span>
              </div>
            {%- endif -%}
          </div>
        {%- endif -%}

        <!-- Collapsible Product Information Sections -->
        <div class="product-details-accordion">
          {%- if section.settings.show_product_details -%}
            <details class="product-detail-section">
              <summary class="detail-header">
                <span>Product Details</span>
                <span class="detail-toggle">▼</span>
              </summary>
              <div class="detail-content">
                {%- if section.settings.product_details_content != blank -%}
                  {{ section.settings.product_details_content }}
                {%- elsif product.description != blank -%}
                  {{ product.description }}
                {%- else -%}
                  <p>Product details will be displayed here.</p>
                {%- endif -%}
              </div>
            </details>
          {%- endif -%}

          {%- if section.settings.show_size_fit -%}
            <details class="product-detail-section">
              <summary class="detail-header">
                <span>Size & Fit</span>
                <span class="detail-toggle">▼</span>
              </summary>
              <div class="detail-content">
                {%- if section.settings.size_fit_content != blank -%}
                  {{ section.settings.size_fit_content }}
                {%- else -%}
                  <p>Size and fit information will be displayed here.</p>
                {%- endif -%}
              </div>
            </details>
          {%- endif -%}

          {%- if section.settings.show_material_care -%}
            <details class="product-detail-section">
              <summary class="detail-header">
                <span>Material & Care</span>
                <span class="detail-toggle">▼</span>
              </summary>
              <div class="detail-content">
                {%- if section.settings.material_care_content != blank -%}
                  {{ section.settings.material_care_content }}
                {%- else -%}
                  <p>Material and care instructions will be displayed here.</p>
                {%- endif -%}
              </div>
            </details>
          {%- endif -%}

          {%- if section.settings.show_delivery_returns -%}
            <details class="product-detail-section">
              <summary class="detail-header">
                <span>Delivery & Returns</span>
                <span class="detail-toggle">▼</span>
              </summary>
              <div class="detail-content">
                {%- if section.settings.delivery_returns_content != blank -%}
                  {{ section.settings.delivery_returns_content }}
                {%- else -%}
                  <p>Delivery and returns information will be displayed here.</p>
                {%- endif -%}
              </div>
            </details>
          {%- endif -%}

          {%- if section.settings.show_model_info -%}
            <details class="product-detail-section">
              <summary class="detail-header">
                <span>Model Info</span>
                <span class="detail-toggle">▼</span>
              </summary>
              <div class="detail-content">
                {%- if section.settings.model_info_content != blank -%}
                  {{ section.settings.model_info_content }}
                {%- else -%}
                  <p>Model information will be displayed here.</p>
                {%- endif -%}
              </div>
            </details>
          {%- endif -%}
        </div>
      </div>
    </div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  // Image Slider with Dot Navigation
  const slider = document.getElementById('productSlider');
  const sliderTrack = document.getElementById('sliderTrack');
  const dots = document.querySelectorAll('.dot');
  const images = document.querySelectorAll('.slider-image');
  const isScrollAnimation = slider.classList.contains('scroll-animation');
  const isSwipeAnimation = slider.classList.contains('swipe-animation');
  let currentSlide = 0;
  let isScrolling = false;
  let lastScrollY = window.scrollY;

  // Touch/swipe variables for mobile
  let touchStartX = 0;
  let touchStartY = 0;
  let touchEndX = 0;
  let touchEndY = 0;
  const minSwipeDistance = 50;

  // Dot click functionality
  dots.forEach((dot, index) => {
    dot.addEventListener('click', () => {
      goToSlide(index);
    });
  });

  // Enhanced scroll functionality
  let scrollTimeout;
  let scrollDirection = 0;
  
  if (isScrollAnimation) {
    // For scroll animation, listen to slider's internal scroll
    slider.addEventListener('scroll', () => {
      if (isScrolling) return;
      
      clearTimeout(scrollTimeout);
      scrollTimeout = setTimeout(() => {
        handleSliderScroll();
      }, 100); // Debounce scroll events
    }, { passive: true });
  } else {
    // For fade animation, listen to window scroll
    window.addEventListener('scroll', () => {
      if (isScrolling) return;
      
      const currentScrollY = window.scrollY;
      scrollDirection = currentScrollY > lastScrollY ? 1 : -1;
      lastScrollY = currentScrollY;
      
      clearTimeout(scrollTimeout);
      scrollTimeout = setTimeout(() => {
        handleScrollChange();
      }, 16); // ~60fps for smooth updates
    }, { passive: true });
  }

  function handleSliderScroll() {
    const sliderScrollTop = slider.scrollTop;
    const sliderHeight = slider.clientHeight;
    
    // Calculate which image should be active based on scroll position
    let activeIndex = 0;
    for (let i = 0; i < images.length; i++) {
      const imageTop = images[i].offsetTop;
      const imageHeight = images[i].clientHeight;
      
      if (sliderScrollTop >= imageTop - sliderHeight / 2 && 
          sliderScrollTop < imageTop + imageHeight - sliderHeight / 2) {
        activeIndex = i;
        break;
      }
    }
    
    if (activeIndex !== currentSlide) {
      // Update dots without triggering scroll
      dots[currentSlide].classList.remove('active');
      dots[activeIndex].classList.add('active');
      currentSlide = activeIndex;
    }
  }

  function handleScrollChange() {
    const sliderElement = document.querySelector('.product-slider');
    if (!sliderElement) return;
    
    const sliderRect = sliderElement.getBoundingClientRect();
    const windowHeight = window.innerHeight;
    
    // Check if slider is in viewport
    if (sliderRect.top < windowHeight && sliderRect.bottom > 0) {
      // Calculate scroll progress within the slider area
      const sliderVisibleHeight = Math.min(sliderRect.bottom, windowHeight) - Math.max(sliderRect.top, 0);
      const visibilityRatio = sliderVisibleHeight / windowHeight;
      
      if (visibilityRatio > 0.3) { // Only when slider is significantly visible
        const scrollProgress = Math.max(0, Math.min(1, (windowHeight - sliderRect.top) / windowHeight));
        const targetSlide = Math.floor(scrollProgress * images.length);
        const clampedSlide = Math.max(0, Math.min(images.length - 1, targetSlide));
        
        if (clampedSlide !== currentSlide) {
          goToSlide(clampedSlide);
        }
      }
    }
  }

  // Touch/swipe functionality for mobile
  slider.addEventListener('touchstart', (e) => {
    touchStartX = e.changedTouches[0].screenX;
    touchStartY = e.changedTouches[0].screenY;
  }, { passive: true });

  slider.addEventListener('touchend', (e) => {
    touchEndX = e.changedTouches[0].screenX;
    touchEndY = e.changedTouches[0].screenY;
    
    // Handle swipes for fade animation and swipe animation, let scroll animation handle touch naturally
    if (!isScrollAnimation) {
      handleSwipe();
    }
  }, { passive: true });

  function handleSwipe() {
    const swipeDistanceX = touchEndX - touchStartX;
    const swipeDistanceY = touchEndY - touchStartY;
    
    // Check if it's a horizontal swipe (not vertical scroll)
    if (Math.abs(swipeDistanceX) > Math.abs(swipeDistanceY) && Math.abs(swipeDistanceX) > minSwipeDistance) {
      if (swipeDistanceX > 0) {
        // Swipe right - previous image
        const prevSlide = currentSlide > 0 ? currentSlide - 1 : images.length - 1;
        goToSlide(prevSlide);
      } else {
        // Swipe left - next image
        const nextSlide = currentSlide < images.length - 1 ? currentSlide + 1 : 0;
        goToSlide(nextSlide);
      }
    }
    // Vertical swipe for image navigation (alternative to scroll)
    else if (Math.abs(swipeDistanceY) > minSwipeDistance && Math.abs(swipeDistanceY) > Math.abs(swipeDistanceX)) {
      if (swipeDistanceY < 0) {
        // Swipe up - next image
        const nextSlide = currentSlide < images.length - 1 ? currentSlide + 1 : 0;
        goToSlide(nextSlide);
      } else {
        // Swipe down - previous image
        const prevSlide = currentSlide > 0 ? currentSlide - 1 : images.length - 1;
        goToSlide(prevSlide);
      }
    }
  }

  // Mouse wheel support for desktop
  slider.addEventListener('wheel', (e) => {
    if (isScrollAnimation) {
      // Let the scroll animation handle wheel events naturally
      return;
    }
    
    e.preventDefault();
    
    if (isScrolling) return;
    
    const delta = e.deltaY > 0 ? 1 : -1;
    
    if (delta > 0) {
      // Scroll down - next image
      const nextSlide = currentSlide < images.length - 1 ? currentSlide + 1 : 0;
      goToSlide(nextSlide);
    } else {
      // Scroll up - previous image
      const prevSlide = currentSlide > 0 ? currentSlide - 1 : images.length - 1;
      goToSlide(prevSlide);
    }
  }, { passive: false });

  function goToSlide(index) {
    if (index === currentSlide || index < 0 || index >= images.length) return;
    
    isScrolling = true;
    
    if (isScrollAnimation) {
      // Scroll animation - scroll to specific image
      const targetImage = images[index];
      if (targetImage) {
        targetImage.scrollIntoView({ 
          behavior: 'smooth', 
          block: 'start',
          inline: 'nearest'
        });
      }
    } else {
      // Fade animation - toggle opacity
      images[currentSlide].classList.remove('active');
      images[index].classList.add('active');
    }
    
    // Update dots
    dots[currentSlide].classList.remove('active');
    dots[index].classList.add('active');
    
    currentSlide = index;
    
    // Reset scrolling flag after transition
    setTimeout(() => {
      isScrolling = false;
    }, 400);
  }

  // Keyboard navigation
  document.addEventListener('keydown', (e) => {
    const sliderRect = slider.getBoundingClientRect();
    const isSliderVisible = sliderRect.top < window.innerHeight && sliderRect.bottom > 0;
    
    if (!isSliderVisible) return;
    
    if (e.key === 'ArrowUp' || e.key === 'ArrowLeft') {
      e.preventDefault();
      const prevSlide = currentSlide > 0 ? currentSlide - 1 : images.length - 1;
      goToSlide(prevSlide);
    } else if (e.key === 'ArrowDown' || e.key === 'ArrowRight') {
      e.preventDefault();
      const nextSlide = currentSlide < images.length - 1 ? currentSlide + 1 : 0;
      goToSlide(nextSlide);
    }
  });

  // Variant Selection and Product Updates
  const form = document.getElementById('product-form');
  const variantInputs = document.querySelectorAll('.variant-input');
  const priceElement = document.getElementById('productPrice');
  const priceSummaryElement = document.getElementById('productPriceSummary');
  const addToCartBtn = document.getElementById('addToCartBtn');
  const addToCartBtnSummary = document.getElementById('addToCartBtnSummary');
  const variantIdInput = document.getElementById('variantId');

  // Product variant data
  const variants = [
    {%- for variant in product.variants -%}
      {
        "id": {{ variant.id }},
        "price": {{ variant.price }},
        "compare_at_price": {{ variant.compare_at_price | default: 0 }},
        "available": {{ variant.available | json }},
        "option1": {{ variant.option1 | json }},
        "option2": {{ variant.option2 | json }},
        "option3": {{ variant.option3 | json }},
        "featured_media": {
          {%- if variant.featured_media -%}
            "id": {{ variant.featured_media.id }}
          {%- else -%}
            "id": null
          {%- endif -%}
        }
      }{%- unless forloop.last -%},{%- endunless -%}
    {%- endfor -%}
  ];

  // Handle variant selection
  variantInputs.forEach(input => {
    input.addEventListener('change', updateProduct);
  });

  function updateProduct() {
    const selectedOptions = [];
    
    // Get selected values for each option
    for (let i = 1; i <= 3; i++) {
      const checkedInput = document.querySelector(`input[data-option-position="${i}"]:checked`);
      if (checkedInput) {
        selectedOptions.push(checkedInput.value);
      }
    }

    // Find matching variant
    const selectedVariant = variants.find(variant => {
      return selectedOptions.every((option, index) => {
        const variantOption = index === 0 ? variant.option1 : 
                             index === 1 ? variant.option2 : 
                             variant.option3;
        return variantOption === option;
      });
    });

    if (selectedVariant) {
      // Update price
      const formattedPrice = new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
      }).format(selectedVariant.price / 100);
      
      priceElement.textContent = formattedPrice;
      if (priceSummaryElement) {
        priceSummaryElement.textContent = formattedPrice;
      }
      
      // Update add to cart button
      variantIdInput.value = selectedVariant.id;
      
      if (selectedVariant.available) {
        addToCartBtn.disabled = false;
        addToCartBtn.textContent = 'Add to Shopping Bag';
        if (addToCartBtnSummary) {
          addToCartBtnSummary.disabled = false;
          addToCartBtnSummary.textContent = 'Add to Shopping Bag';
        }
      } else {
        addToCartBtn.disabled = true;
        addToCartBtn.textContent = 'Sold Out';
        if (addToCartBtnSummary) {
          addToCartBtnSummary.disabled = true;
          addToCartBtnSummary.textContent = 'Sold Out';
        }
      }

      // Update URL without page reload
      const url = new URL(window.location);
      url.searchParams.set('variant', selectedVariant.id);
      window.history.replaceState({}, '', url);
    }

    // Update visual selection states
    updateSelectionStyles();
  }

  function updateSelectionStyles() {
    // Update color swatch selections
    document.querySelectorAll('.color-swatch').forEach(swatch => {
      swatch.classList.remove('selected');
    });
    document.querySelectorAll('.color-swatches input:checked').forEach(input => {
      document.querySelector(`label[for="${input.id}"]`).classList.add('selected');
    });

    // Update size option selections
    document.querySelectorAll('.size-option').forEach(option => {
      option.classList.remove('selected');
    });
    document.querySelectorAll('.size-options input:checked').forEach(input => {
      document.querySelector(`label[for="${input.id}"]`).classList.add('selected');
    });
  }

  // Initialize selection styles
  updateSelectionStyles();
});
</script>

{% schema %}
{
  "name": "Product",
  "tag": "section",
  "class": "section",
  "settings": [
    {
      "type": "header",
      "content": "Desktop layout"
    },
    {
      "type": "select",
      "id": "layout_style",
      "label": "Desktop layout",
      "default": "stack",
      "options": [
        { "value": "stack",  "label": "Stack (single column)" },
        { "value": "50-50",  "label": "Two-column 50/50" },
        { "value": "60-40",  "label": "Two-column 60/40" }
      ],
      "info": "Mobile remains single column."
    },
    {
      "type": "checkbox",
      "id": "gallery_two_cols",
      "label": "Media gallery: two columns on desktop",
      "default": true
    },
    {
      "type": "header",
      "content": "Product Information"
    },
    {
      "type": "checkbox",
      "id": "show_new_badge",
      "label": "Show 'NEW' badge",
      "default": true,
      "info": "Display a 'NEW' badge above the product title"
    },
    {
      "type": "checkbox",
      "id": "show_stock_status",
      "label": "Show stock status",
      "default": true,
      "info": "Display 'In Stock' or 'Out of Stock' status"
    },
    {
      "type": "checkbox",
      "id": "show_points_program",
      "label": "Show points program",
      "default": false,
      "info": "Display points earned for this product"
    },
    {
      "type": "text",
      "id": "points_text",
      "label": "Points program text",
      "default": "6200 Oner Points",
      "info": "Text to display for points program"
    },
    {
      "type": "select",
      "id": "mobile_pagination_style",
      "label": "Mobile pagination style",
      "default": "dots",
      "options": [
        { "value": "dots",     "label": "Dots (default)" },
        { "value": "segments", "label": "Segments (lines)" },
        { "value": "none",     "label": "Hidden on mobile" }
      ],
      "info": "Applies to screens under 750px. Desktop pagination remains unchanged."
    },
    {
      "type": "header",
      "content": "Slider animation"
    },
    {
      "type": "select",
      "id": "slider_animation",
      "label": "Animation style",
      "options": [
        {
          "value": "fade",
          "label": "Fade"
        },
        {
          "value": "scroll",
          "label": "Vertical scroll"
        },
        {
          "value": "swipe",
          "label": "Swipe (mobile only)"
        }
      ],
      "default": "fade",
      "info": "Choose transition style. Swipe enables touch navigation on mobile devices."
    },
    {
      "type": "header",
      "content": "Shipping & Features"
    },
    {
      "type": "checkbox",
      "id": "show_shipping_info",
      "label": "Show shipping information",
      "default": true,
      "info": "Display free shipping thresholds and delivery info"
    },
    {
      "type": "text",
      "id": "free_standard_threshold",
      "label": "Free standard shipping threshold",
      "default": "$120 CAD",
      "info": "Amount needed for free standard shipping"
    },
    {
      "type": "text",
      "id": "free_express_threshold",
      "label": "Free express shipping threshold",
      "default": "$220 CAD",
      "info": "Amount needed for free express shipping"
    },
    {
      "type": "checkbox",
      "id": "show_easy_returns",
      "label": "Show easy returns",
      "default": true,
      "info": "Display easy returns message"
    },
    {
      "type": "header",
      "content": "Collapsible Sections"
    },
    {
      "type": "checkbox",
      "id": "show_product_details",
      "label": "Show Product Details section",
      "default": true
    },
    {
      "type": "richtext",
      "id": "product_details_content",
      "label": "Product Details content",
      "default": "<p>Product details will be displayed here.</p>",
      "info": "Leave blank to use product description"
    },
    {
      "type": "checkbox",
      "id": "show_size_fit",
      "label": "Show Size & Fit section",
      "default": true
    },
    {
      "type": "richtext",
      "id": "size_fit_content",
      "label": "Size & Fit content",
      "default": "<p>Size and fit information will be displayed here.</p>"
    },
    {
      "type": "checkbox",
      "id": "show_material_care",
      "label": "Show Material & Care section",
      "default": true
    },
    {
      "type": "richtext",
      "id": "material_care_content",
      "label": "Material & Care content",
      "default": "<p>Material and care instructions will be displayed here.</p>"
    },
    {
      "type": "checkbox",
      "id": "show_delivery_returns",
      "label": "Show Delivery & Returns section",
      "default": true
    },
    {
      "type": "richtext",
      "id": "delivery_returns_content",
      "label": "Delivery & Returns content",
      "default": "<p>Delivery and returns information will be displayed here.</p>"
    },
    {
      "type": "checkbox",
      "id": "show_model_info",
      "label": "Show Model Info section",
      "default": true
    },
    {
      "type": "richtext",
      "id": "model_info_content",
      "label": "Model Info content",
      "default": "<p>Model information will be displayed here.</p>"
    },
    {
      "type": "header",
      "content": "Color swatch preview"
    },
    {
      "type": "checkbox",
      "id": "enable_color_hover_preview",
      "label": "Enable color hover preview",
      "default": true,
      "info": "Hover over color swatches to preview variant images"
    },
    {
      "type": "range",
      "id": "revert_delay_ms",
      "min": 0,
      "max": 1000,
      "step": 100,
      "unit": "ms",
      "label": "Preview revert delay",
      "default": 0,
      "info": "Delay before reverting hover preview (0 = instant)"
    },
    {
      "type": "header",
      "content": "Product media"
    },
    {
      "type": "checkbox",
      "id": "enable_zoom",
      "label": "Enable image zoom",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "hide_variants",
      "label": "Hide sold out variants",
      "default": false
    }
  ],
  "disabled_on": {
    "groups": ["header", "footer"]
  }
}
{% endschema %}

{% style %}
/* --- SEGMENTS: consistent gaps + longer active with width animation --- */
#product-{{ section.id }} {
  --seg-l: 18px;                  /* inactive length */
  --seg-l-active: 30px;           /* active length (Gentle Monster vibe) */
  --seg-h: 2px;                   /* bar height */
  --seg-gap: clamp(6px, 1.6vw, 10px);
}

@media (max-width: 749px){
  /* keep pagination on the slider, 4% from bottom */
  #product-{{ section.id }} .product-slider{ position: relative; }
  #product-{{ section.id }} .product-slider .slider-dots{
    position: absolute !important;
    left: 50%;
    bottom: max(4%, env(safe-area-inset-bottom)) !important;
    top: auto !important;
    transform: translateX(-50%);
    gap: var(--seg-gap) !important;
    margin: 0 !important;
    z-index: 20;
  }

  /* Stronger, scoped rules override the earlier fixed-width + !important block */
  #product-{{ section.id }}.product--mobile-segments .product-slider .slider-dots .dot{
    width: var(--seg-l) !important;
    height: var(--seg-h) !important;
    border-radius: var(--seg-h) !important;
    background: rgba(255,255,255,.45) !important;
    box-shadow: none !important;
    padding: 0 !important; margin: 0 !important;
    transform: none !important;        /* keeps the gaps uniform */
    will-change: width, background-color, opacity;
    transition:
      width 260ms cubic-bezier(.33,0,.2,1),
      background-color 200ms ease,
      opacity 200ms ease;
  }
  #product-{{ section.id }}.product--mobile-segments .product-slider .slider-dots .dot.active{
    width: var(--seg-l-active) !important; /* longer active dash */
    background: #fff !important;
    opacity: 1 !important;
  }
}

/* --- SWIPE MODE: no fade, instant switch --- */
#product-{{ section.id }} .product-slider.swipe-animation .slider-image{
  transition: none !important;     /* disable fade */
}
/* keep inactive hidden / active visible; JS already toggles .active */
#product-{{ section.id }} .product-slider.swipe-animation .slider-image{ opacity: 0; }
#product-{{ section.id }} .product-slider.swipe-animation .slider-image.active{ opacity: 1; }
{% endstyle %}

<script>
// Wishlist functionality
document.addEventListener('DOMContentLoaded', function() {
  const wishlistBtn = document.getElementById('addToWishlist');
  if (wishlistBtn) {
    wishlistBtn.addEventListener('click', function() {
      // Add wishlist functionality here
      const icon = this.querySelector('.wishlist-icon');
      if (icon.textContent === '♡') {
        icon.textContent = '♥';
        this.style.color = '#e74c3c';
      } else {
        icon.textContent = '♡';
        this.style.color = '#000';
      }
    });
  }

  // Collapsible sections animation
  const detailSections = document.querySelectorAll('.product-detail-section');
  detailSections.forEach(section => {
    const summary = section.querySelector('.detail-header');
    if (summary) {
      summary.addEventListener('click', function() {
        // Small delay to allow the details element to toggle
        setTimeout(() => {
          const toggle = this.querySelector('.detail-toggle');
          if (section.hasAttribute('open')) {
            toggle.style.transform = 'rotate(180deg)';
          } else {
            toggle.style.transform = 'rotate(0deg)';
          }
        }, 10);
      });
    }
  });
});
</script>
