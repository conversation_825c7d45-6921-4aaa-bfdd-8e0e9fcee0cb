{%- comment -%}
  Collection Product Grid Section

  Purpose: Main collection page with Shopify filtering, sorting, and pagination
  - Native Shopify Search & Discovery integration
  - Responsive product grid with customizable image ratios
  - Promo blocks can be inserted after specified product positions
  - Performance optimized with lazy loading and responsive images
  - Accessible with proper headings and ARIA labels
{%- endcomment -%}

{% liquid
  assign products_per_page = section.settings.page_size | default: 24
  assign image_ratio = section.settings.image_ratio | default: 'adapt'
  assign show_vendor = section.settings.show_vendor | default: false
  assign color_scheme = section.settings.color_scheme | default: 'scheme-1'
  
  comment
    Calculate responsive grid columns
  endcomment
  assign cols_desktop = section.settings.columns_desktop | default: 4
  assign cols_tablet = section.settings.columns_tablet | default: 2
  assign cols_mobile = section.settings.columns_mobile | default: 1

  assign current_collection = collection
  assign on_collection_page = false
  if request.page_type == 'collection' or template.name == 'collection'
    assign on_collection_page = true
  endif
  if on_collection_page == false and section.settings.featured_collection != blank
    assign current_collection = section.settings.featured_collection
  endif
%}

<div class="collection-product-grid color-{{ color_scheme }} section-{{ section.id }}-padding"
     data-swatch-mode="{{ section.settings.swatch_display_mode | default: 'hover_overlay' }}"
     style="
       --collection-title-font-weight: {{ section.settings.collection_title_font_weight | default: 'normal' }};
       --collection-description-font-weight: {{ section.settings.collection_description_font_weight | default: 'normal' }};
     ">
  <div class="collection-product-grid__inner page-width">
    
    {% comment %} Collection Header {% endcomment %}
  {%- assign show_title = false -%}
  {%- if current_collection.title != blank and section.settings.show_collection_title -%}
    {%- assign show_title = true -%}
  {%- endif -%}
  {%- assign show_desc = false -%}
  {%- if current_collection.description != blank and section.settings.show_collection_description -%}
    {%- assign show_desc = true -%}
  {%- endif -%}
  {%- if show_title or show_desc -%}
      <div class="collection-product-grid__header">
        {%- if current_collection.title != blank and section.settings.show_collection_title -%}
          <h1 class="collection-product-grid__title size-{{ section.settings.collection_title_font_size | default: 'large' }}">{{ current_collection.title }}</h1>
        {%- endif -%}
        {%- if current_collection.description != blank and section.settings.show_collection_description -%}
          <div class="collection-product-grid__description size-{{ section.settings.collection_description_font_size | default: 'medium' }} rte">
            {{ current_collection.description }}
          </div>
        {%- endif -%}
      </div>
    {%- endif -%}

    
        {% comment %} Filter and Sort Bar {% endcomment %}
        {% if on_collection_page %}
          {% if section.settings.enable_filtering or section.settings.enable_sorting %}
            <div class="collection-product-grid__toolbar">
              {% if section.settings.enable_filtering and collection.filters != empty %}
                <button
                  class="collection-product-grid__filter-toggle"
                  aria-expanded="false"
                  aria-controls="CollectionFilters-{{ section.id }}"
                >
                  Filter
                </button>
              {% endif %}
              
              {% if section.settings.enable_sorting %}
                <div class="collection-product-grid__sort">
                  <label for="SortBy-{{ section.id }}" class="visually-hidden">Sort by</label>
                  <select
                    id="SortBy-{{ section.id }}"
                    class="collection-product-grid__sort-select"
                    onchange="window.location.href = this.value"
                  >
                    {%- assign sort_by = collection.sort_by | default: collection.default_sort_by -%}
                    {%- for option in collection.sort_options -%}
                      <option
                        value="{{ option.value | escape }}"
                        {%- if option.value == sort_by %} selected="selected"{% endif -%}
                      >
                        {{ option.name | escape }}
                      </option>
                    {%- endfor -%}
                  </select>
                </div>
              {% endif %}
            </div>
          {% endif %}
        {% endif %}
    {% comment %} Filters Panel {% endcomment %}
    {% if on_collection_page and section.settings.enable_filtering and collection.filters != empty %}
      <div class="collection-product-grid__filters" id="CollectionFilters-{{ section.id }}">
        {% render 'facets',
          results: collection,
          enable_filtering: true,
          enable_sorting: false,
          filter_type: 'horizontal',
          section_id: section.id
        %}
      </div>
    {% endif %}

    {% comment %} Product Grid {% endcomment %}
    <!-- DEBUG: show_swatches = {{ section.settings.show_swatches }}, enable_hover_preview = {{ section.settings.enable_hover_preview }} -->
    {% if current_collection != blank %}
        <div
          class="collection-product-grid__grid"
          style="
            --cols-desktop: {{ cols_desktop }};
            --cols-tablet: {{ cols_tablet }};
            --cols-mobile: {{ cols_mobile }};
          "
        >
          {%- assign product_index = 0 -%}
          {%- for product in current_collection.products limit: products_per_page -%}
            {%- assign product_index = product_index | plus: 1 -%}

            {%- comment -%} Check for promo blocks to insert {%- endcomment -%}
            {%- for block in section.blocks -%}
              {%- if block.type == 'promo' and block.settings.insert_after == product_index -%}
                {%- assign has_promo = false -%}
                {%- if block.settings.promo_type == 'image' and block.settings.image != blank -%}
                  {%- assign has_promo = true -%}
                {%- elsif block.settings.promo_type == 'richtext' and block.settings.html != blank -%}
                  {%- assign has_promo = true -%}
                {%- endif -%}

                {%- if has_promo -%}
                  <div class="collection-product-grid__promo-block" {{ block.shopify_attributes }}>
                    {%- if block.settings.promo_type == 'image' -%}
                      <div class="collection-product-grid__promo-image">
                        {{ block.settings.image | image_url: width: 600 | image_tag: loading: 'lazy', class: 'collection-product-grid__promo-img', widths: '300, 400, 500, 600', alt: block.settings.image.alt }}
                      </div>
                    {%- else -%}
                      <div class="collection-product-grid__promo-content rte">
                        {{ block.settings.html }}
                      </div>
                    {%- endif -%}
                  </div>
                {%- endif -%}
              {%- endif -%}
            {%- endfor -%}

            {%- comment -%} Render product card {%- endcomment -%}
            {%- render 'card-product',
              product: product,
              collection: current_collection,
              image_ratio: image_ratio,
              show_vendor: show_vendor,
              show_secondary_image: section.settings.show_secondary_image,
              show_quick_add: section.settings.show_quick_add,
              enable_favorites: section.settings.enable_favorites,
              show_new_badges: section.settings.show_new_badges,
              show_swatches: section.settings.show_swatches,
              enable_hover_preview: section.settings.enable_hover_preview,
              max_swatches: section.settings.max_swatches,
              columns: cols_desktop,
              grid_position: product_index,
              section_id: section.id
            -%}
          {%- endfor -%}
        </div>
    {% else %}
      <p class="color-foreground" style="opacity:.7">Select a collection in section settings.</p>
    {% endif %}
  </div>
</div>

{% style %}
  .collection-product-grid {
    padding-top: 3rem;
    padding-bottom: 3rem;
  }

 .collection-product-grid__inner {
    max-width: var(--page-width);
    margin: 0 auto;
    padding: 0 1.5rem;
  }

  .collection-product-grid__header {
    text-align: center;
    margin-bottom: 4rem;
  }

  .collection-product-grid__title {
    font-weight: var(--collection-title-font-weight);
    margin-bottom: 1rem;
  }

  /* Title font sizes - responsive with clamp() */
  .collection-product-grid__title.size-small {
    font-size: clamp(1.25rem, 2.5vw, 1.75rem);
  }
  .collection-product-grid__title.size-medium {
    font-size: clamp(1.8rem, 3vw, 2.8rem);
  }
  .collection-product-grid__title.size-large {
    font-size: clamp(2.6rem, 4vw, 4.2rem);
  }
  .collection-product-grid__title.size-extra-large {
    font-size: clamp(3.2rem, 5vw, 5.6rem);
  }

  .collection-product-grid__description {
    max-width: 60rem;
    margin: 0 auto;
    font-weight: var(--collection-description-font-weight);
    line-height: 1.6;
    color: rgba(var(--color-foreground), 0.8);
  }

  /* Description font sizes - responsive with clamp() */
  .collection-product-grid__description.size-small {
    font-size: clamp(1rem, 1.5vw, 1.125rem);
  }
  .collection-product-grid__description.size-medium {
    font-size: clamp(1.2rem, 2vw, 1.4rem);
  }
  .collection-product-grid__description.size-large {
    font-size: clamp(1.4rem, 2.2vw, 1.6rem);
  }

  .collection-product-grid__toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    gap: 2rem;
  }

  .collection-product-grid__filter-toggle {
    background: rgb(var(--color-background));
    border: 0.1rem solid rgba(var(--color-foreground), 0.2);
    color: rgb(var(--color-foreground));
    padding: 1rem 2rem;
    border-radius: 0.4rem;
    cursor: pointer;
    font-size: 1.4rem;
    transition: all 0.2s ease;
  }

  .collection-product-grid__filter-toggle:hover {
    border-color: rgb(var(--color-foreground));
  }

  .collection-product-grid__sort {
    position: relative;
  }

  .collection-product-grid__sort-select {
    background: rgb(var(--color-background));
    border: 0.1rem solid rgba(var(--color-foreground), 0.2);
    color: rgb(var(--color-foreground));
    padding: 1rem;
  }

  .collection-product-grid__grid {
    display: grid;
    gap: 1.5rem;
    grid-template-columns: repeat(var(--cols-mobile), 1fr);
  }

  @media screen and (min-width: 750px) {
    .collection-product-grid__grid {
      gap: 2rem;
      grid-template-columns: repeat(var(--cols-tablet), 1fr);
    }
 }

  @media screen and (min-width: 990px) {
    .collection-product-grid__grid {
      grid-template-columns: repeat(var(--cols-desktop), 1fr);
    }
 }

  .collection-product-grid__promo-block {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    padding: 2rem;
    background: rgba(var(--color-foreground), 0.05);
    border-radius: 0.4rem;
  }

  .collection-product-grid__promo-image {
    margin-bottom: 1.5rem;
  }

  .collection-product-grid__promo-img {
    width: 100%;
    height: auto;
  }

  .collection-product-grid__promo-content {
    font-size: 1.4rem;
  }

 .collection-product-grid__pagination {
    margin-top: 4rem;
  }

  .collection-product-grid__promo-block:empty {
    display: none;
  }

  .collection-product-grid__inner.page-width {
    max-width: none;
    padding-left: max(12px, 2vw);
    padding-right: max(12px, 2vw);
  }
{% endstyle %}

{{ 'collection-grid-utilities.css' | asset_url | stylesheet_tag }}

{% schema %}
{
  "name": "Collection Product Grid",
  "settings": [
    {
      "type": "collection",
      "id": "featured_collection",
      "label": "Collection"
    },
    {
      "type": "checkbox",
      "id": "show_collection_title",
      "label": "Show collection title",
      "default": true
    },
    {
      "type": "select",
      "id": "collection_title_font_weight",
      "label": "Collection title font weight",
      "default": "normal",
      "options": [
        {
          "value": "normal",
          "label": "Normal"
        },
        {
          "value": "bold",
          "label": "Bold"
        }
      ]
    },
    {
      "type": "select",
      "id": "collection_title_font_size",
      "label": "Collection title font size",
      "default": "large",
      "options": [
        {
          "value": "small",
          "label": "Small"
        },
        {
          "value": "medium",
          "label": "Medium"
        },
        {
          "value": "large",
          "label": "Large"
        },
        {
          "value": "extra-large",
          "label": "Extra Large"
        }
      ]
    },
    {
      "type": "checkbox",
      "id": "show_collection_description",
      "label": "Show collection description",
      "default": true
    },
    {
      "type": "select",
      "id": "collection_description_font_weight",
      "label": "Collection description font weight",
      "default": "normal",
      "options": [
        {
          "value": "normal",
          "label": "Normal"
        },
        {
          "value": "bold",
          "label": "Bold"
        }
      ]
    },
    {
      "type": "select",
      "id": "collection_description_font_size",
      "label": "Collection description font size",
      "default": "medium",
      "options": [
        {
          "value": "small",
          "label": "Small"
        },
        {
          "value": "medium",
          "label": "Medium"
        },
        {
          "value": "large",
          "label": "Large"
        }
      ]
    },
    {
      "type": "range",
      "id": "page_size",
      "label": "Products per page",
      "min": 4,
      "max": 48,
      "step": 4,
      "default": 24
    },
    {
      "type": "header",
      "content": "Filtering and sorting"
    },
    {
      "type": "checkbox",
      "id": "enable_filtering",
      "label": "Enable filtering",
      "default": true,
      "info": "Customize filters with the Search & Discovery app"
    },
    {
      "type": "checkbox",
      "id": "enable_sorting",
      "label": "Enable sorting",
      "default": true
    },
    {
      "type": "header",
      "content": "Grid layout"
    },
    {
      "type": "select",
      "id": "image_ratio",
      "label": "Image ratio",
      "options": [
        {
          "value": "adapt",
          "label": "Adapt to image"
        },
        {
          "value": "portrait",
          "label": "Portrait"
        },
        {
          "value": "square",
          "label": "Square"
        },
        {
          "value": "landscape",
          "label": "Landscape"
        }
      ],
      "default": "adapt"
    },
    {
      "type": "range",
      "id": "columns_desktop",
      "label": "Columns on desktop",
      "min": 1,
      "max": 5,
      "step": 1,
      "default": 4
    },
    {
      "type": "range",
      "id": "columns_tablet",
      "label": "Columns on tablet",
      "min": 1,
      "max": 4,
      "step": 1,
      "default": 2
    },
    {
      "type": "range",
      "id": "columns_mobile",
      "label": "Columns on mobile",
      "min": 1,
      "max": 3,
      "step": 1,
      "default": 1
    },
    {
      "type": "select",
      "id": "color_scheme",
      "label": "Color scheme",
      "options": [
        {
          "value": "scheme-1",
          "label": "Scheme 1"
        },
        {
          "value": "scheme-2",
          "label": "Scheme 2"
        }
      ],
      "default": "scheme-1"
    },
    {
      "type": "header",
      "content": "Product card"
    },
    {
      "type": "checkbox",
      "id": "show_vendor",
      "label": "Show vendor",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "show_secondary_image",
      "label": "Show secondary image on hover",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "show_quick_add",
      "label": "Show quick add button",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "enable_favorites",
      "label": "Enable favorites",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_new_badges",
      "label": "Show NEW badges",
      "default": true
    },
    {
      "type": "header",
      "content": "Color swatches"
    },
    {
      "type": "checkbox",
      "id": "show_swatches",
      "label": "Show color swatches",
      "default": true,
      "info": "Display color variant options as swatches"
    },
    {
      "type": "checkbox",
      "id": "enable_hover_preview",
      "label": "Enable card hover preview",
      "default": true,
      "info": "Hover over color swatches to preview variant images"
    },
    {
      "type": "select",
      "id": "swatch_display_mode",
      "label": "Swatch display style",
      "options": [
        {
          "value": "below_card",
          "label": "Below card (always visible)"
        },
        {
          "value": "hover_overlay",
          "label": "Hover overlay (show on card hover)"
        }
      ],
      "default": "hover_overlay",
      "info": "Choose how variant options are displayed"
    },
    {
      "type": "range",
      "id": "max_swatches",
      "label": "Maximum swatches to show",
      "min": 3,
      "max": 10,
      "step": 1,
      "default": 5,
      "info": "Additional colors will show as '+N' indicator"
    }
  ],
  "blocks": [
    {
      "type": "promo",
      "name": "Promo block",
      "settings": [
        {
          "type": "range",
          "id": "insert_after",
          "label": "Insert after product",
          "min": 1,
          "max": 50,
          "step": 1,
          "default": 4
        },
        {
          "type": "select",
          "id": "promo_type",
          "label": "Type",
          "options": [
            {
              "value": "image",
              "label": "Image"
            },
            {
              "value": "richtext",
              "label": "Rich text"
            }
          ],
          "default": "image"
        },
        {
          "type": "image_picker",
          "id": "image",
          "label": "Image"
        },
        {
          "type": "richtext",
          "id": "html",
          "label": "Content"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Collection product grid"
    }
  ]
}
{% endschema %}

<script src="{{ 'variant-hover-clean.js' | asset_url }}" defer></script>
