{%- comment -%}
  Media Card for Collection Grid
  
  Purpose: Renders media content (image/video) that spans 2 columns in collection grid
  - Fixed height to match product card containers
  - Support for images and videos
  - Optional overlay content (text, buttons)
  - Responsive design with proper accessibility

  Usage: {% render 'card-media', media_settings: block.settings, grid_span: 2 %}

  Parameters:
  - media_settings: block settings object containing media configuration
  - grid_span: number of columns to span (default 2)
  - loading: loading behavior ('lazy' or 'eager')
  - sizes: responsive sizes attribute
{%- endcomment -%}

{%- liquid
  assign grid_span = grid_span | default: 2
  assign loading_behavior = loading | default: 'lazy'
  assign media_object_position = media_settings.object_position | default: 'center'
-%}

<div class="media-card" 
     style="--media-span: {{ grid_span }}; --card-aspect: {{ aspect_value }}; --card-object-position: {{ media_object_position }};"
     data-media-card>
  
  <div class="media-card__container">
    {%- if media_settings.video != blank -%}
      {%- comment -%} Video Content {%- endcomment -%}
      <div class="media-card__video">
        {%- if media_settings.video.sources[0].url contains '.mp4' -%}
          <video 
            class="media-card__video-element"
            width="800"
            height="600"
            autoplay 
            muted 
            loop 
            playsinline
            {% if loading_behavior == 'lazy' %}loading="lazy"{% endif %}
            poster="{{ media_settings.video.preview_image | image_url: width: 800 }}"
            aria-label="{{ media_settings.video_alt | default: media_settings.heading | escape }}">
            <source src="{{ media_settings.video.sources[0].url }}" type="video/mp4">
            {%- comment -%} Fallback for unsupported video {%- endcomment -%}
            <img src="{{ media_settings.video.preview_image | image_url: width: 800 }}" 
                 width="800"
                 height="600"
                 alt="{{ media_settings.video_alt | default: media_settings.heading | escape }}"
                 loading="{{ loading_behavior }}">
          </video>
        {%- else -%}
          {%- comment -%} External video (YouTube, Vimeo, etc.) {%- endcomment -%}
          {{ media_settings.video | video_tag: 
              image_size: '800x',
              loading: loading_behavior,
              autoplay: true,
              loop: true,
              muted: true,
              controls: false,
              class: 'media-card__video-element'
          }}
        {%- endif -%}
      </div>
    {%- elsif media_settings.image != blank -%}
      {%- comment -%} Image Content {%- endcomment -%}
      <div class="media-card__image">
        <img 
          src="{{ media_settings.image | image_url: width: 800 }}"
          srcset="{{ media_settings.image | image_url: width: 400 }} 400w,
                  {{ media_settings.image | image_url: width: 600 }} 600w,
                  {{ media_settings.image | image_url: width: 800 }} 800w,
                  {{ media_settings.image | image_url: width: 1000 }} 1000w,
                  {{ media_settings.image | image_url: width: 1200 }} 1200w"
          sizes="{{ sizes | default: '(min-width: 1200px) 50vw, (min-width: 600px) 67vw, 100vw' }}"
          alt="{{ media_settings.image_alt | default: media_settings.heading | escape }}"
          width="800"
          height="600"
          loading="{{ loading_behavior }}"
          decoding="async"
          class="media-card__image-element">
      </div>
    {%- endif -%}

    {%- comment -%} Overlay Content {%- endcomment -%}
    {%- if media_settings.heading != blank or media_settings.text != blank or media_settings.button_label != blank -%}
      <div class="media-card__overlay">
        <div class="media-card__content">
          {%- if media_settings.heading != blank -%}
            <h3 class="media-card__heading">{{ media_settings.heading | escape }}</h3>
          {%- endif -%}
          
          {%- if media_settings.text != blank -%}
            <div class="media-card__text">{{ media_settings.text }}</div>
          {%- endif -%}
          
          {%- if media_settings.button_label != blank and media_settings.button_link != blank -%}
            <a href="{{ media_settings.button_link }}" 
               class="media-card__button button button--primary"
               {% if media_settings.button_link contains 'http' %}target="_blank" rel="noopener noreferrer"{% endif %}>
              {{ media_settings.button_label | escape }}
            </a>
          {%- endif -%}
        </div>
      </div>
    {%- endif -%}
  </div>
</div>

<style>
  .media-card {
    grid-column: span var(--media-span, 2);
    position: relative;
    overflow: hidden;
    background-color: rgb(var(--color-background));
    height: 100%; /* Fill the grid cell */
  }

  .media-card__container {
    position: relative;
    width: 100%;
    height: 100%; /* Fill the grid cell height */
    overflow: hidden;
  }

  .media-card__image,
  .media-card__video {
    position: absolute;
    inset: 0;
    width: 100%;
    height: 100%;
  }

  .media-card__image-element,
  .media-card__video-element {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: {{ media_object_position }};
    display: block;
  }

  .media-card__overlay {
    position: absolute;
    inset: 0;
    background: linear-gradient(
      to bottom,
      transparent 0%,
      transparent 30%,
      rgba(0, 0, 0, 0.3) 70%,
      rgba(0, 0, 0, 0.7) 100%
    );
    display: flex;
    align-items: flex-end;
    padding: 2rem;
    color: white;
  }

  .media-card__content {
    width: 100%;
    max-width: 400px;
  }

  .media-card__heading {
    font-size: 1.75rem;
    font-weight: 700;
    line-height: 1.2;
    margin: 0 0 0.75rem;
    color: white;
  }

  .media-card__text {
    font-size: 1rem;
    line-height: 1.5;
    margin: 0 0 1.25rem;
    color: rgba(255, 255, 255, 0.9);
  }

  .media-card__button {
    display: inline-flex;
    align-items: center;
    padding: 0.75rem 1.5rem;
    font-size: 0.875rem;
    font-weight: 600;
    text-decoration: none;
    border-radius: 4px;
    transition: all 0.2s ease;
    background-color: white;
    color: rgb(var(--color-foreground));
    border: none;
  }

  .media-card__button:hover {
    background-color: rgba(255, 255, 255, 0.9);
    transform: translateY(-1px);
  }

  /* Responsive adjustments */
  @media (max-width: 768px) {
    .media-card {
      grid-column: span 1; /* Single column on mobile */
    }

    .media-card__overlay {
      padding: 1.5rem;
    }

    .media-card__heading {
      font-size: 1.5rem;
    }

    .media-card__text {
      font-size: 0.875rem;
    }
  }

  @media (max-width: 480px) {
    .media-card__overlay {
      padding: 1rem;
    }

    .media-card__heading {
      font-size: 1.25rem;
    }
  }

  /* Accessibility improvements */
  @media (prefers-reduced-motion: reduce) {
    .media-card__video-element {
      animation-play-state: paused !important;
    }
    
    .media-card__button {
      transform: none !important;
    }
  }
</style>
