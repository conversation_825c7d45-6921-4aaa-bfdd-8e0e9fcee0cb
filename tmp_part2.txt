                {% endif %}
              </div>
            {% endif %}
          {% endfor %}
        </div>

      {% if paginate.pages > 1 %}
        <nav class="pagination" role="navigation" aria-label="Pagination">
          {% if paginate.previous %}
            <a class="pagination__prev" href="{{ paginate.previous.url }}" rel="prev">{{ 'general.pagination.previous' | t | default: 'Previous' }}</a>
          {% endif %}

          {% for part in paginate.parts %}
            {% if part.is_link %}
              <a class="pagination__page" href="{{ part.url }}">{{ part.title }}</a>
            {% else %}
              {% if part.title == paginate.current_page %}
                <span class="pagination__page is-current" aria-current="page">{{ part.title }}</span>
              {% else %}
                <span class="pagination__ellipsis" aria-hidden="true">{{ part.title }}</span>
              {% endif %}
            {% endif %}
          {% endfor %}

          {% if paginate.next %}
            <a class="pagination__next" href="{{ paginate.next.url }}" rel="next">{{ 'general.pagination.next' | t | default: 'Next' }}</a>
          {% endif %}
        </nav>
      {% endif %}
      {% endpaginate %}
      {%- else -%}
        <div class="collection-empty">
          <h2>{{ 'collections.general.no_matches' | t | default: 'No products found' }}</h2>
          <p>{{ 'collections.general.no_products_html' | t | default: 'Try adjusting your filters or adding products to this collection.' }}</p>
        </div>
      {%- endif -%}
  </div>
</section>

{% schema %}
{
  "name": "Collection",
  "tag": "section",
  "class": "section",
  "settings": [
    {
      "type": "header",
      "content": "Collection display"
    },
    {
      "type": "checkbox",
      "id": "show_collection_title",
      "label": "Show collection title",
      "default": true
    },
    {
      "type": "select",
      "id": "collection_title_font_weight",
      "label": "Collection title font weight",
      "default": "normal",
      "options": [
        {
          "value": "normal",
          "label": "Normal"
        },
        {
          "value": "bold",
          "label": "Bold"
        }
      ]
    },
    {
      "type": "select",
      "id": "collection_title_font_size",
      "label": "Collection title font size",
      "default": "large",
      "options": [
        {
          "value": "small",
          "label": "Small"
        },
        {
          "value": "medium",
          "label": "Medium"
        },
        {
          "value": "large",
          "label": "Large"
        },
        {
          "value": "extra-large",
          "label": "Extra Large"
        }
      ]
    },
    {
      "type": "checkbox",
      "id": "show_collection_description",
      "label": "Show collection description",
      "default": true
    },
    {
      "type": "select",
      "id": "collection_description_font_weight",
      "label": "Collection description font weight",
      "default": "normal",
      "options": [
        {
          "value": "normal",
          "label": "Normal"
        },
        {
          "value": "bold",
          "label": "Bold"
        }
      ]
    },
    {
      "type": "select",
      "id": "collection_description_font_size",
      "label": "Collection description font size",
      "default": "medium",
      "options": [
        {
          "value": "small",
          "label": "Small"
        },
        {
          "value": "medium",
          "label": "Medium"
        },
        {
          "value": "large",
          "label": "Large"
        }
      ]
    },
    {
      "type": "range",
      "id": "products_per_page",
      "min": 4,
      "max": 48,
      "step": 4,
      "default": 20,
      "label": "Products per page"
    },
    {
      "type": "header",
      "content": "Grid layout"
    },
    {
      "type": "range",
      "id": "columns_desktop",
      "min": 2,
      "max": 6,
      "step": 1,
      "default": 4,
      "label": "Columns on desktop"
    },
    {
      "type": "range",
      "id": "columns_tablet",
      "min": 2,
      "max": 4,
      "step": 1,
      "default": 3,
      "label": "Columns on tablet"
    },
    {
      "type": "range",
      "id": "columns_mobile",
      "min": 1,
      "max": 3,
      "step": 1,
      "default": 2,
      "label": "Columns on mobile"
    },
    {
      "type": "header",
      "content": "Color swatches on cards"
    },
    {
      "type": "checkbox",
      "id": "show_swatches_on_cards",
      "label": "Show color swatches on product cards",
      "default": true,
      "info": "Display color options as swatches on collection cards"
    },
    {
      "type": "checkbox",
      "id": "enable_card_hover_preview",
      "label": "Enable card hover preview",
      "default": true,
      "info": "Hover over color swatches to preview variant images"
    },
    {
      "type": "range",
      "id": "max_swatches_per_product",
      "min": 3,
      "max": 8,
      "step": 1,
      "default": 5,
      "label": "Maximum swatches per product",
      "info": "Limit number of color swatches shown on each card"
    },
    {
      "type": "select",
      "id": "swatch_display_style",
      "label": "Swatch display style",
      "options": [
        {
          "value": "below_card",
          "label": "Below card (always visible)"
        },
        {
          "value": "hover_overlay",
          "label": "Hover overlay (show on card hover)"
        }
      ],
      "default": "below_card",
      "info": "Choose how variant options are displayed"
    },
    {
      "type": "select",
      "id": "card_click_behavior",
      "label": "Swatch click behavior",
      "default": "link_with_variant",
      "options": [
        {
          "value": "link_with_variant",
          "label": "Go to product with variant selected"
        },
        {
          "value": "quick_add_variant",
          "label": "Add to cart via quick add"
        }
      ],
      "info": "How swatch clicks behave on collection cards"
    },
    {
      "type": "header",
      "content": "Card appearance"
    },
    {
      "type": "select",
      "id": "card_image_aspect_ratio",
      "label": "Card image aspect ratio",
      "default": "3/4",
      "options": [
        {
