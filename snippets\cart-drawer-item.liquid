{%- assign item = item -%}
<article class="cart-drawer-item" data-line data-line-key="{{ item.key }}" data-variant-id="{{ item.variant_id }}" data-unit-price="{{ item.final_price }}">
  <a href="{{ item.url }}" class="cdi-media">
    {{ item.image | image_url: width: 80 | image_tag: alt: item.title }}
  </a>

  <div class="cdi-main">
    <a href="{{ item.url }}" class="cdi-title">{{ item.product.title }}</a>
    <div class="cdi-price">{{ item.final_price | money }}</div>

    {% if item.options_with_values.size > 0 %}
      <div class="cdi-variants">
        {% for opt in item.options_with_values %}
          <span class="cdi-chip">{{ opt.value }}</span>
        {% endfor %}
      </div>
    {% endif %}

    <div class="cdi-actions">
      <div class="cdi-qty" role="group" aria-label="Quantity selector for {{ item.product.title | escape }}">
        <button type="button" data-qty-minus aria-label="Decrease quantity" tabindex="0">−</button>
        <input
          type="number"
          value="{{ item.quantity }}"
          data-line-qty
          inputmode="numeric"
          min="0"
          aria-label="Quantity"
        >
        <button type="button" data-qty-plus aria-label="Increase quantity" tabindex="0">+</button>
      </div>
      <button type="button" class="cdi-remove" data-remove aria-label="Remove">×</button>
    </div>

    <div class="cdi-subtotal">
      <strong data-line-subtotal>{{ item.final_line_price | money }}</strong>
    </div>
  </div>
</article>

<style>
.cart-drawer-item {
  display: grid;
  grid-template-columns: 80px 1fr;
  gap: 12px;
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
}

.cart-drawer-item:last-child {
  border-bottom: none;
}

.cdi-media {
  display: block;
  width: 80px;
  height: 80px;
  border-radius: 6px;
  overflow: hidden;
  background: #f8f8f8;
}

.cdi-media img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  border-radius: 6px;
}

.cdi-main {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.cdi-title {
  font-size: 0.875rem;
  font-weight: 500;
  color: #000;
  text-decoration: none;
  line-height: 1.3;
}

.cdi-title:hover {
  text-decoration: underline;
}

.cdi-price {
  font-size: 0.875rem;
  color: #666;
}

.cdi-variants {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.cdi-chip {
  font-size: 0.75rem;
  padding: 2px 6px;
  background: #f5f5f5;
  border-radius: 4px;
  color: #666;
}

.cdi-actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 8px;
}

.cdi-qty {
  display: flex;
  align-items: center;
  border: 1px solid #e5e5e5;
  border-radius: 6px;
  overflow: hidden;
}

.cdi-qty button {
  width: 28px;
  height: 28px;
  background: #f8f8f8;
  border: none;
  cursor: pointer;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cdi-qty button:hover {
  background: #e5e5e5;
}

.cdi-qty input {
  width: 40px;
  height: 28px;
  border: none;
  text-align: center;
  font-size: 0.875rem;
  background: #fff;
}

.cdi-remove {
  background: none;
  border: none;
  color: #999;
  cursor: pointer;
  font-size: 1.25rem;
  padding: 4px;
  border-radius: 4px;
  transition: color 0.2s;
}

.cdi-remove:hover {
  color: #666;
  background: #f5f5f5;
}

.cdi-subtotal {
  font-size: 0.875rem;
  text-align: right;
  margin-top: 4px;
}
</style>