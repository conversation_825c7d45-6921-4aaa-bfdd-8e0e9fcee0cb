<section id="announcement-{{ section.id }}" class="announcement-bar" data-section-type="announcement"
  data-auto-rotate="{{ section.settings.auto_rotate }}"
  data-rotation-speed="{{ section.settings.rotation_speed | times: 1000 }}">
  {% assign dismissed_key = 'announcement_dismissed_' | append: section.id %}

  <style>
    :root {
      /* default header height; will be updated by JS for exact value */
      --header-h: 64px;
    }
    .announcement-bar {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      z-index: 200; /* Higher than header z-index to ensure it stays on top */
      width: 100%;
      box-sizing: border-box;
      /* Responsive height: minimum 43px, scales with content and screen size */
      min-height: clamp(43px, 4vh, 60px);
    }

    /* Neutral state (natural flow): announcement bar participates in flow */
    body.header--natural-flow .announcement-bar {
      position: static;
      top: auto;
      left: auto;
      right: auto;
      z-index: auto;
    }

    /* Hero overlay mode: announcement bar stays fixed, header floats below it */
    body.header--hero-overlay .announcement-bar {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      z-index: 200; /* Above header overlay */
    }

    body.header--hero-overlay .header {
      top: var(--announcement-height, 0px);
    }
    .announcement-bar__inner {
      display: grid;
      place-items: center; /* perfect centering */
      padding: 0; /* remove vertical padding to avoid top bias */
      position: relative;
      font-size: clamp(0.75rem, 2.5vw, 0.875rem);
      font-weight: 500;
      letter-spacing: 0.5px;
      width: 100vw;
      margin-left: calc(-50vw + 50%);
      min-height: inherit; /* Inherit from parent announcement-bar */
      line-height: 1.4;
      box-sizing: border-box;
    }
    .announcement-bar__content {
      flex: 1;
      max-width: var(--page-width, 1200px);
      margin: 0 auto;
      padding: 0 clamp(1rem, 3vw, 2rem);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 100%;
    }
    .announcement-bar__slider {
      position: relative;
      width: 100%;
      min-height: 0; /* allow content to size naturally */
      display: grid;
      place-items: center;
    }
    .announcement-bar__slide {
      position: relative;
      display: none;
      text-align: center;
      padding: clamp(8px, 1.5vh, 16px) 3rem; /* Responsive vertical padding */
      width: 100%;
      min-height: inherit; /* Inherit responsive height */
    }
    .announcement-bar__slide.active {
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: inherit; /* Inherit responsive height */
    }
    .announcement-bar__message {
      font-size: inherit;
      text-transform: uppercase;
      font-weight: inherit;
      letter-spacing: inherit;
      text-align: center;
      width: 100%;
      word-wrap: break-word;
      overflow-wrap: break-word;
      line-height: inherit;
    }
    .announcement-bar__link {
      color: inherit;
      text-decoration: none;
      text-transform: uppercase;
      font-weight: inherit;
      letter-spacing: inherit;
      text-align: center;
      width: 100%;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      flex-wrap: wrap;
      gap: 0.25rem;
      word-wrap: break-word;
      overflow-wrap: break-word;
      line-height: inherit;
    }
    .announcement-bar__link:hover {
      text-decoration: underline;
    }
    .announcement-bar__cta {
      font-weight: inherit;
      margin-left: 0.5rem;
    }
    .announcement-bar__close {
      background: transparent;
      border: 0;
      color: inherit;
      font-size: 1.25rem;
      cursor: pointer;
      position: absolute;
      right: var(--page-margin);
      top: 50%;
      transform: translateY(-50%);
      z-index: 1;
      opacity: 0.7;
      transition: opacity 0.2s ease;
    }
    .announcement-bar__close:hover {
      opacity: 1;
    }
    .announcement-bar__nav {
      display: flex;
      gap: 0.5rem;
      justify-content: center;
      margin-top: 0.5rem;
    }
    .announcement-bar__dot {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background: var(--dot-color, #ffffff);
      opacity: 0.5;
      cursor: pointer;
      transition: all 0.2s ease;
      border: none;
      padding: 0;
    }
    .announcement-bar__dot.active {
      opacity: 1;
      transform: scale(1.2);
    }
    .announcement-bar--hidden-mobile {
      display: none !important;
    }
    @media (max-width: 599px) {
      .announcement-bar {
        /* Ensure minimum touch target size on mobile */
        min-height: clamp(44px, 5vh, 56px);
      }
      .announcement-bar__slide {
        padding: 0 2.5rem;
      }
      .announcement-bar__close {
        right: 1rem;
      }
    }
  </style>

  {% javascript %}
    (() => {
      const root = document.querySelector('[data-section-type="announcement"]');
      if (!root) return;

      // Update CSS variables for announcement height
      const updateAnnouncementHeight = () => {
        const announcementHeight = root.getBoundingClientRect().height; // allow sub-pixels
        document.documentElement.style.setProperty('--announcement-height', `${announcementHeight}px`);
      };

      // Initial setup
      updateAnnouncementHeight();

      // Update on resize
      window.addEventListener('resize', updateAnnouncementHeight);

      // Update when announcement content changes
      const resizeObserver = new ResizeObserver(updateAnnouncementHeight);
      resizeObserver.observe(root);

      const slides = root.querySelectorAll('.announcement-bar__slide');
      const dots = root.querySelectorAll('.announcement-bar__dot');
      let currentSlide = 0;
      let autoRotateInterval;

      const showSlide = (index) => {
        // Remove active class from all slides and dots
        slides.forEach(slide => slide.classList.remove('active'));
        dots.forEach(dot => dot.classList.remove('active'));

        // Add active class to current slide and dot
        if (slides[index]) {
          slides[index].classList.add('active');
          currentSlide = index;
        }
        if (dots[index]) {
          dots[index].classList.add('active');
        }
      };

      const nextSlide = () => {
        const next = (currentSlide + 1) % slides.length;
        showSlide(next);
      };

      const startAutoRotate = () => {
        const autoRotate = root.dataset.autoRotate === 'true';
        const rotationSpeed = parseInt(root.dataset.rotationSpeed);
        if (autoRotate && rotationSpeed > 0) {
          autoRotateInterval = setInterval(nextSlide, rotationSpeed);
        }
      };

      const stopAutoRotate = () => {
        if (autoRotateInterval) {
          clearInterval(autoRotateInterval);
          autoRotateInterval = null;
        }
      };

      // Initialize first slide
      if (slides.length > 0) {
        showSlide(0);
        if (slides.length > 1) {
          startAutoRotate();

          // Pause auto-rotation on hover
          root.addEventListener('mouseenter', stopAutoRotate);
          root.addEventListener('mouseleave', startAutoRotate);

          // Manual navigation via dots
          dots.forEach((dot, index) => {
            dot.addEventListener('click', () => {
              stopAutoRotate();
              showSlide(index);
              startAutoRotate();
            });
          });
        }
      }

      // Close functionality
      const button = root.querySelector('[data-announcement-close]');
      if (!button) return;
      button.addEventListener('click', () => {
        stopAutoRotate();
        const el = button.closest('.announcement-bar__inner');
        if (el) {
          el.style.display = 'none';
          // Update CSS variables when announcement is hidden
          document.documentElement.style.setProperty('--announcement-height', '0px');
        }
        try {
          const sectionId = root.id.split('-')[1];
          const key = 'announcement_dismissed_' + sectionId;
          document.cookie = key + '=true; path=/; max-age=' + (60 * 60 * 24 * 30);
        } catch (e) { /* ignore */ }
      });
    })();
  {% endjavascript %}

  {% if section.settings.dismissible and request.cookies[dismissed_key] == 'true' %}
    {% comment %}Already dismissed via cookie - do not render{% endcomment %}
    <script>
      // Set announcement height to 0 when dismissed
      document.documentElement.style.setProperty('--announcement-height', '0px');
    </script>
  {% else %}
    <div class="announcement-bar__inner{% unless section.settings.show_on_mobile %} announcement-bar--hidden-mobile{% endunless %}" role="region" aria-label="{{ 'general.announcement.title' | t }}" style="background-color: {{ section.settings.background_color }}; color: {{ section.settings.text_color }}; --dot-color: {{ section.settings.dot_color }};">
      <div class="announcement-bar__content" style="text-align: {{ section.settings.alignment }};">
        <div class="announcement-bar__slider">
          {% if section.blocks.size > 0 %}
            {% for block in section.blocks %}
              {% if block.settings.message != blank %}
                <div class="announcement-bar__slide" {{ block.shopify_attributes }}>
                  {% if block.settings.show_link and block.settings.link != blank and block.settings.link_label != blank %}
                    <a class="announcement-bar__link" href="{{ block.settings.link }}" style="{% if block.settings.link_color != blank %}color: {{ block.settings.link_color }};{% endif %}">
                      {{ block.settings.message | escape }}
                      <span class="announcement-bar__cta">{{ block.settings.link_label }}</span>
                    </a>
                  {% else %}
                    <span class="announcement-bar__message">{{ block.settings.message | escape }}</span>
                  {% endif %}
                </div>
              {% endif %}
            {% endfor %}
          {% else %}
            <div class="announcement-bar__slide active">
              <span class="announcement-bar__message">Welcome to our store</span>
            </div>
          {% endif %}
        </div>

        {% if section.blocks.size > 1 and section.settings.show_navigation %}
          <div class="announcement-bar__nav">
            {% for block in section.blocks %}
              {% if block.settings.message != blank %}
                <button type="button" class="announcement-bar__dot" aria-label="{{ 'general.announcement.go_to_announcement' | t }} {{ forloop.index }}"></button>
              {% endif %}
            {% endfor %}
          </div>
        {% endif %}
      </div>

      {% if section.settings.dismissible %}
        <button type="button" class="announcement-bar__close" aria-label="{{ 'general.announcement.close' | t }}" data-announcement-close>
          <span aria-hidden="true">&times;</span>
        </button>
      {% endif %}
    </div>
  {% endif %}

  {% schema %}
  {
    "name": "📢 Announcement Bar",
    "tag": "section",
    "class": "announcement-section",
    "settings": [
      {
        "type": "paragraph",
        "content": "Create rotating announcements to highlight promotions, store updates, or important information. Add up to 3 different messages using the blocks below."
      },
      {
        "type": "header",
        "content": "🎨 Appearance"
      },
      {
        "type": "color",
        "id": "background_color",
        "label": "Background color",
        "default": "#1a1a1a"
      },
      {
        "type": "color",
        "id": "text_color",
        "label": "Text color",
        "default": "#ffffff"
      },
      {
        "type": "select",
        "id": "alignment",
        "label": "Text alignment",
        "options": [
          { "value": "left", "label": "Left" },
          { "value": "center", "label": "Center" },
          { "value": "right", "label": "Right" }
        ],
        "default": "center"
      },
      {
        "type": "header",
        "content": "🧱 Layout"
      },
      {
        "type": "header",
        "content": "🔄 Auto-Rotation (Multiple Announcements)"
      },
      {
        "type": "checkbox",
        "id": "auto_rotate",
        "label": "Automatically rotate between announcements",
        "default": true,
        "info": "When you have multiple announcements, they will automatically cycle"
      },
      {
        "type": "range",
        "id": "rotation_speed",
        "label": "Time between rotations",
        "min": 2,
        "max": 10,
        "step": 1,
        "default": 5,
        "unit": "s",
        "info": "How long each announcement is displayed before switching"
      },
      {
        "type": "checkbox",
        "id": "show_navigation",
        "label": "Show navigation dots",
        "default": false,
        "info": "Display clickable dots to manually navigate between announcements"
      },
      {
        "type": "color",
        "id": "dot_color",
        "label": "Navigation dot color",
        "default": "#ffffff",
        "info": "Color of the navigation dots (only visible when navigation is enabled)"
      },
      {
        "type": "header",
        "content": "⚙️ Display Options"
      },
      {
        "type": "checkbox",
        "id": "dismissible",
        "label": "Allow visitors to close the announcement",
        "default": false,
        "info": "Adds a close button for visitors to hide the announcement bar"
      },
      {
        "type": "checkbox",
        "id": "show_on_mobile",
        "label": "Show on mobile devices",
        "default": true
      }
    ],
    "blocks": [
      {
        "type": "announcement",
        "name": "📢 Announcement Text",
        "limit": 3,
        "settings": [
          {
            "type": "paragraph",
            "content": "Create engaging announcements that rotate automatically. Add up to 3 different messages."
          },
          {
            "type": "textarea",
            "id": "message",
            "label": "📝 Announcement Message",
            "placeholder": "Enter your announcement text here...",
            "info": "This text will appear in the announcement bar"
          },
          {
            "type": "header",
            "content": "🔗 Link Settings (Optional)"
          },
          {
            "type": "checkbox",
            "id": "show_link",
            "label": "Make this announcement clickable",
            "default": false,
            "info": "Turn this announcement into a clickable link"
          },
          {
            "type": "text",
            "id": "link_label",
            "label": "Call-to-action text",
            "placeholder": "Shop now, Learn more, etc.",
            "default": "Shop now",
            "info": "Text that appears after your message (e.g., 'Shop now')"
          },
          {
            "type": "url",
            "id": "link",
            "label": "Link destination",
            "info": "Where should this announcement link to?"
          },
          {
            "type": "color",
            "id": "link_color",
            "label": "Link color override",
            "info": "Leave empty to use the same color as the text"
          }
        ]
      }
    ],
    "presets": [
      {
        "name": "Announcement Bar",
        "settings": {
          "background_color": "#1a1a1a",
          "text_color": "#ffffff",
          "auto_rotate": true,
          "rotation_speed": 4,
          "show_navigation": false,
          "dot_color": "#ffffff",
          "dismissible": false,
          "show_on_mobile": true,
          "alignment": "center"
        },
        "blocks": [
          {
            "type": "announcement",
            "settings": {
              "message": "OUR STORES",
              "show_link": false
            }
          },
          {
            "type": "announcement",
            "settings": {
              "message": "FREE SHIPPING ON US ORDERS OVER $150",
              "show_link": false
            }
          },
          {
            "type": "announcement",
            "settings": {
              "message": "NEW PRODUCTS    ENDS IN 00 | 0H | 0M | 0S",
              "show_link": false
            }
          }
        ]
      }
    ],
    "max_blocks": 3
  }
  {% endschema %}
</section>
